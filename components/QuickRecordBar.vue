<template>
  <view class="quick-record-bar" :class="currentTheme">
    <view class="bar">
      <!-- 左：云吸入口（图标/文字） -->
      <button class="cloud-btn" @click="onCloudEntry">云吸</button>

      <!-- 中：当前烟盒名（左右滑动切换） -->
      <view
        class="box-display"
        :class="{ swiping: isSwiping }"
        @click="onBoxDisplayClick"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
        @touchcancel="onTouchCancel"
      >
        <text class="box-title">{{ currentBoxName }}</text>
      </view>

      <!-- 右：步进器 + 记录按钮 -->
      <view class="controls">
        <view class="stepper">
          <button class="step-btn" @touchstart.prevent="onStepPressStart('dec')" @touchend="onStepPressEnd" @touchcancel="onStepPressEnd">
            <uni-icons type="minus" size="28" color="currentColor" />
          </button>
          <view class="amount">
            <text class="amount-value">{{ stepAmount }}</text>
            <text class="amount-unit">{{ unit }}</text>
          </view>
          <button class="step-btn" @touchstart.prevent="onStepPressStart('inc')" @touchend="onStepPressEnd" @touchcancel="onStepPressEnd">
            <uni-icons type="plus" size="28" color="currentColor" />
          </button>
        </view>
        <button
          class="btn-primary record-btn"
          :disabled="!currentBox || stepAmount <= 0"
          @click="onRecordButtonClick"
        >
          <template>记录</template>
        </button>
      </view>
    </view>
    <!-- 撤销气泡：显示在底部条上方，不覆盖按钮区域 -->
    <transition name="undo-bubble">
      <view v-if="undo.visible" class="undo-bubble-wrapper">
        <view class="undo-bubble">
          <text class="undo-text">是否撤销最近记录的 {{ undo.amount }}{{ undo.unit }}</text>
          <button class="undo-btn" @click="onUndo">撤销</button>
        </view>
      </view>
    </transition>
  </view>
</template>

<script setup>
import { computed, reactive, watch, ref, onBeforeUnmount } from 'vue'
import { useTheme } from '../composables/useTheme.js'

const props = defineProps({
  activeBoxes: { type: Array, required: true },
  selectedBoxId: { type: String, default: '' },
  hapticsEnabled: { type: Boolean, default: true },
  // 当无昵称时，用于回退显示品牌名称
  getBrandName: { type: Function, default: null }
})

const emit = defineEmits(['update:selectedBoxId', 'record', 'go-virtual'])

const { currentTheme } = useTheme()

const localSelected = ref(props.selectedBoxId)
watch(() => props.selectedBoxId, v => { localSelected.value = v })

const currentBox = computed(() => props.activeBoxes.find(b => b.id === localSelected.value) || props.activeBoxes[0])
const currentBoxName = computed(() => {
  if (!currentBox.value) return '请选择烟盒'
  const fallback = typeof props.getBrandName === 'function' ? (props.getBrandName(currentBox.value.brandId) || '') : ''
  return currentBox.value.nickname || fallback || '未命名烟盒'
})

const undo = reactive({ visible: false, recordId: '', amount: 0, unit: '' })
let undoTimer = null

// 步进数量与单位
const stepAmount = ref(1)
const unit = computed(() => currentBox.value?.type === 'electronic' ? '口' : '根')

// 切换烟盒时重置步进器数量为 1
watch(() => localSelected.value, () => {
  stepAmount.value = 1
})

// 步进器：短按±1，长按连发
let holdTimer = null
let repeatTimer = null
let pressDirection = null
let longPressActivated = false
const onStepPressStart = (direction) => {
  pressDirection = direction === 'inc' ? 'inc' : 'dec'
  longPressActivated = false
  clearTimeout(holdTimer)
  clearInterval(repeatTimer)
  haptic()
  holdTimer = setTimeout(() => {
    longPressActivated = true
    repeatTimer = setInterval(() => {
      if (pressDirection === 'inc') increaseAmount()
      else decreaseAmount()
      haptic()
    }, 120)
  }, 350)
}
const onStepPressEnd = () => {
  clearTimeout(holdTimer)
  clearInterval(repeatTimer)
  // 短按：未进入长按连发，则执行一次
  if (!longPressActivated && pressDirection) {
    if (pressDirection === 'inc') increaseAmount()
    else decreaseAmount()
  }
  pressDirection = null
  longPressActivated = false
}

const switchPrev = () => {
  if (!props.activeBoxes.length) return
  const idx = props.activeBoxes.findIndex(b => b.id === currentBox.value.id)
  const next = (idx - 1 + props.activeBoxes.length) % props.activeBoxes.length
  haptic()
  emit('update:selectedBoxId', props.activeBoxes[next].id)
}

const switchNext = () => {
  if (!props.activeBoxes.length) return
  const idx = props.activeBoxes.findIndex(b => b.id === currentBox.value.id)
  const next = (idx + 1) % props.activeBoxes.length
  haptic()
  emit('update:selectedBoxId', props.activeBoxes[next].id)
}

// 盒名区域滑动切换
let startX = 0
let tracking = false
const isSwiping = ref(false)
const lastSwipeAt = ref(0)
const onTouchStart = (e) => {
  try { startX = e.changedTouches?.[0]?.clientX || 0; tracking = true; isSwiping.value = true } catch (_) { startX = 0; tracking = false; isSwiping.value = false }
}
const onTouchMove = () => {}
const onTouchEnd = (e) => {
  if (!tracking) return
  tracking = false
  isSwiping.value = false
  const endX = e.changedTouches?.[0]?.clientX || 0
  const dx = endX - startX
  const THRESHOLD = 40
  if (dx > THRESHOLD) { switchPrev(); lastSwipeAt.value = Date.now() }
  else if (dx < -THRESHOLD) { switchNext(); lastSwipeAt.value = Date.now() }
}
const onTouchCancel = () => { tracking = false; isSwiping.value = false }

const onBoxDisplayClick = () => {
  // 若刚发生滑动，忽略紧随其后的点击
  if (Date.now() - lastSwipeAt.value < 250) return
  switchNext()
}

const haptic = () => {
  if (!props.hapticsEnabled) return
  if (typeof uni !== 'undefined' && uni.vibrateShort) {
    try { uni.vibrateShort() } catch (_) {}
  }
}

const onCloudEntry = () => {
  emit('go-virtual')
}

const onRecord = () => {
  if (!currentBox.value || !stepAmount.value || stepAmount.value <= 0) return
  doRecord(Number(stepAmount.value))
}

const increaseAmount = () => { stepAmount.value = Math.max(1, Number(stepAmount.value || 1) + 1) }
const decreaseAmount = () => { stepAmount.value = Math.max(1, Number(stepAmount.value || 1) - 1) }

const doRecord = (amount) => {
  const payload = { boxId: currentBox.value.id, amount, type: currentBox.value.type, source: 'quick-bar' }
  emit('record', payload)
  haptic()
}

const showUndo = (recordId, amount, unit) => {
  undo.visible = true
  undo.recordId = recordId
  undo.amount = amount
  undo.unit = unit
  if (undoTimer) clearTimeout(undoTimer)
  undoTimer = setTimeout(() => { undo.visible = false }, 3000)
}

const onUndo = () => {
  emit('record', { undoRecordId: undo.recordId, source: 'undo-bubble' })
  undo.visible = false
  haptic()
}

defineExpose({ showUndo })

const onRecordButtonClick = () => {
  onRecord()
}

onBeforeUnmount(() => {
  try {
    clearTimeout(holdTimer)
    clearInterval(repeatTimer)
    clearTimeout(undoTimer)
  } catch (_) {}
})
</script>

<style lang="scss" scoped>
.quick-record-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding-bottom: calc(env(safe-area-inset-bottom) + 8rpx);
  background: var(--color-bg-section);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  padding-top: $app-spacing-sm;
  border-top: 1rpx solid var(--color-border);
}

/* 单行条布局 */
.bar {
  display: flex;
  align-items: center;
  gap: $app-spacing-md;
  padding: $app-spacing-sm $app-spacing-md;
}

.cloud-btn {
  height: 64rpx;
  line-height: 64rpx;
  padding: 0 20rpx;
  border-radius: 9999rpx;
  background: var(--color-accent);
  color: var(--color-text-white);
  border: none;
  margin: 0;
  font-size: $app-font-size-sm;
}
.cloud-btn:active { opacity: 0.9; }

.box-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 72rpx;
  border: 1rpx solid var(--color-border);
  border-radius: $app-input-radius;
  padding: 0 16rpx;
}
.box-display.swiping { background-color: var(--color-bg-card); }

.box-title {
  max-width: 100%;
  font-size: $app-font-size-lg;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.controls {
  display: flex;
  align-items: center;
  gap: $app-spacing-sm;
}

.stepper {
  display: flex;
  align-items: center;
  gap: 6rpx;
  background: var(--color-bg-section);
  border: 1px solid var(--color-border);
  border-radius: 9999rpx;
  padding: 0 8rpx;
  height: 64rpx;
}

.step-btn {
  min-width: 64rpx;
  height: 64rpx;
  margin: 0;
  border: none;
  background: transparent;
  color: var(--color-text-primary);
  line-height: 64rpx;
  border-radius: 9999rpx;
}
.step-btn:active {
  background-color: var(--color-bg-card);
  color: var(--color-accent);
}

.amount {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
}
.amount-value { font-size: $app-font-size-lg; color: var(--color-text-primary); }
.amount-unit { font-size: $app-font-size-xs; color: var(--color-text-muted); }

.record-btn {
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 $app-spacing-md;
  font-size: $app-font-size-sm;
}

/* 撤销气泡布局与样式 */
.undo-bubble-wrapper {
  position: fixed; /* 固定定位，确保浮于条之上且不受条内布局影响 */
  left: 0;
  right: 0;
  bottom: calc(env(safe-area-inset-bottom) + 160rpx); /* 位于底部条上方，避免覆盖按钮 */
  display: flex;
  justify-content: center;
  padding: 0 $app-spacing-md;
  pointer-events: none; /* 容器不拦截点击 */
  z-index: 2000; /* 高于条 */
}

.undo-bubble {
  max-width: calc(100% - 2 * $app-spacing-md);
  background: var(--color-bg-popover, var(--color-bg-card));
  color: var(--color-text-primary);
  border: 1rpx solid var(--color-border-strong, var(--color-border));
  border-radius: $app-input-radius;
  box-shadow: var(--shadow-lg, var(--shadow-md));
  padding: 8rpx 12rpx; /* 更紧凑 */
  display: inline-flex;
  align-items: center;
  gap: $app-spacing-sm;
  pointer-events: auto; /* 仅气泡自身可点击 */
}

.undo-text {
  font-size: $app-font-size-xs; /* 较小字号 */
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.undo-btn {
  margin: 0;
  padding: 0 18rpx;
  height: 48rpx; /* 更小按钮高度 */
  line-height: 48rpx;
  border: none;
  border-radius: 9999rpx;
  background: var(--color-accent);
  color: var(--color-text-white);
  font-size: $app-font-size-xs;
}
.undo-btn:active { opacity: 0.9; }

/* 过渡动效：淡入并轻微上浮 */
.undo-bubble-enter-active,
.undo-bubble-leave-active {
  transition: all 180ms ease;
}
.undo-bubble-enter-from,
.undo-bubble-leave-to {
  opacity: 0;
  transform: translateY(8rpx);
}
</style>


