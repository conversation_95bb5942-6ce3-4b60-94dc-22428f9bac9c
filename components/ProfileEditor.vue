<template>
  <view class="profile-editor-modal" v-if="show" @click.self="closeModal">
    <view class="modal-content" @click.stop>
      <view class="modal-header">
        <view class="modal-title">编辑资料</view>
        <view class="close-button" @click="closeModal">
          <text class="close-icon">✕</text>
        </view>
      </view>

      <view class="modal-body">
        <view class="avatar-area">
          <button
            class="avatar-wrapper"
            open-type="chooseAvatar"
            @chooseavatar="onChooseAvatar"
          >
            <image
              :src="displayAvatar"
              class="avatar-image"
              mode="aspectFill"
            />
            <view class="avatar-overlay">
              <text class="iconfont camera-icon">&#xe889;</text>
              <text class="change-text">更换头像</text>
            </view>
          </button>
        </view>

        <view class="nickname-area">
          <view class="nickname-input-wrapper">
            <input
              type="nickname"
              v-model="tempUserData.nickname"
              class="nickname-input"
              placeholder="请输入昵称（6字以内）"
              maxlength="6"
              @click.stop
            />
            <view class="dice-button" @click.stop="generateRandomNickname">
              <text class="iconfont random-icon">&#xe880;</text>
            </view>
          </view>
          <view class="input-hint">点击右侧骰子随机生成昵称</view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="save-button" @click.stop="saveUserProfile">
          确认保存
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  userData: {
    type: Object,
    default: () => ({
      nickname: "",
      avatar: "",
    }),
  },
})

// Emits
const emit = defineEmits(['close', 'save'])

// 响应式数据
const tempUserData = ref({
  nickname: "",
  avatar: "",
})

const tempAvatarUrl = ref("") // 临时头像地址，用于上传
const defaultAvatar = "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/default-avatar.jpg"

const randomNicknames = [
  "烟友",
  "小抽",
  "云雾",
  "青烟",
  "吞云",
  "吐雾",
  "烟客",
  "飘烟",
]

// 计算属性
const displayAvatar = computed(() => {
  return tempAvatarUrl.value || tempUserData.value.avatar || defaultAvatar
})

// 监听用户数据变化
watch(
  () => props.userData,
  (newValue) => {
    tempUserData.value = {
      nickname: newValue.nickname || "",
      avatar: newValue.avatar || "",
    }
  },
  { immediate: true }
)

// 方法
const closeModal = () => {
  emit('close')
}

const generateRandomNickname = () => {
  const randomIndex = Math.floor(Math.random() * randomNicknames.length)
  tempUserData.value.nickname = randomNicknames[randomIndex]
}

const validateUserData = () => {
  if (!tempUserData.value.nickname || tempUserData.value.nickname.trim() === "") {
    uni.showToast({
      title: "请设置昵称",
      icon: "none",
    })
    return false
  }

  if (tempUserData.value.nickname.length > 6) {
    uni.showToast({
      title: "昵称不能超过6个字符",
      icon: "none",
    })
    return false
  }

  return true
}

const saveUserProfile = async () => {
  if (!validateUserData()) {
    return
  }

  try {
    uni.showLoading({
      title: "保存中...",
      mask: true,
    })

    let finalAvatar = tempUserData.value.avatar

    // 检查头像是否为临时地址，如果是则先上传到云存储
    if (tempAvatarUrl.value && isTemporaryAvatar(tempAvatarUrl.value)) {
      console.log("检测到临时头像地址，开始上传到云存储...")
      finalAvatar = await uploadAvatarToCloud(tempAvatarUrl.value)
    }

    // 发送更新事件给父组件
    emit('save', {
      nickname: tempUserData.value.nickname,
      avatar: finalAvatar,
    })

    closeModal()
  } catch (error) {
    console.error("保存用户资料失败:", error)
    uni.showToast({
      title: "保存失败，请重试",
      icon: "none",
    })
  } finally {
    uni.hideLoading()
  }
}

/**
 * 判断是否为临时头像地址
 */
const isTemporaryAvatar = (avatarUrl) => {
  if (!avatarUrl) return false

  // 微信小程序临时文件路径特征
  const tempPatterns = [
    /^wxfile:\/\//, // 微信文件协议
    /\/tmp\//, // 临时目录
    /\/usr\//, // 用户临时目录
    /tempFilePath/, // 临时文件路径
    /\.tmp$/, // .tmp 结尾
  ]

  return tempPatterns.some((pattern) => pattern.test(avatarUrl))
}

const onChooseAvatar = (e) => {
  const { avatarUrl } = e.detail
  console.log("选择头像:", avatarUrl)

  if (avatarUrl) {
    // 保存临时头像地址，等待用户保存时再上传
    tempAvatarUrl.value = avatarUrl
  } else {
    uni.showToast({
      title: "获取头像失败，请重试",
      icon: "none",
    })
  }
}

/**
 * 上传头像到uniCloud云存储
 */
const uploadAvatarToCloud = async (avatarUrl) => {
  try {
    // 生成唯一的文件名
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    const fileName = `avatar_${timestamp}_${randomStr}.jpg`

    // 设置云存储路径
    const cloudPath = `avatars/${fileName}`

    // 上传文件到uniCloud云存储
    const uploadResult = await uniCloud.uploadFile({
      filePath: avatarUrl, // 临时文件路径
      cloudPath: cloudPath, // 云存储中的文件路径
    })

    console.log("头像上传成功:", uploadResult)

    if (uploadResult.fileID) {
      return uploadResult.fileID
    } else {
      throw new Error("上传失败：未获取到文件ID")
    }
  } catch (error) {
    console.error("头像上传失败:", error)
    // 上传失败时，返回空，使用默认头像
    console.warn("头像上传失败，将使用默认头像")
    return ""
  }
}
</script>

<style lang="scss" scoped>
.profile-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-overlay);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 15vh;
  z-index: 999;
}

.modal-content {
  width: 80%;
  max-width: 320px;
  background-color: var(--color-bg-card);
  border-radius: $app-card-radius;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $app-spacing-sm $app-spacing-md;
  background-color: var(--color-bg-card);
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
  font-weight: 600;
}

.close-button {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-muted);
  font-size: $app-font-size-lg;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--color-bg-section);
    color: var(--color-text-secondary);
  }

  &:active {
    background-color: var(--color-border);
    transform: scale(0.95);
  }
}

.close-icon {
  font-size: 18px;
}

.modal-body {
  padding: $app-spacing-md $app-spacing-lg;
}

.avatar-area {
  display: flex;
  justify-content: center;
  margin-bottom: $app-spacing-md;
}

.avatar-wrapper {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 40px;
  border: 2px solid var(--color-border);
  padding: 0;
  background-color: transparent;
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    border-color: var(--color-primary);
    transform: scale(1.02);
  }

  &:active {
    border-color: var(--color-primary-press);
    transform: scale(0.98);
  }
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-overlay);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-text-white);
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 50%;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  margin-bottom: $app-spacing-xs;
}

.change-text {
  font-size: $app-font-size-xs;
}

.nickname-area {
  .nickname-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: $app-spacing-sm;
  }

  .nickname-input {
    flex: 1;
    padding: $app-spacing-sm $app-spacing-md;
    padding-right: 56px;
    height: 44px;
    border: 1px solid var(--color-border);
    border-radius: $app-card-radius;
    font-size: $app-font-size-base;
    background-color: var(--color-bg-card);
    color: var(--color-text-primary);
    transition: all 0.2s ease;

    &:focus {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(28, 126, 214, 0.1);
    }

    &::placeholder {
      color: var(--color-text-muted);
    }
  }

  .dice-button {
    position: absolute;
    right: $app-spacing-md;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: $app-card-radius;
    cursor: pointer;
    font-size: 14px;
    color: var(--color-text-secondary);
    background-color: var(--color-bg-section);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--color-border);
      color: var(--color-text-primary);
    }

    &:active {
      background-color: var(--color-primary);
      color: var(--color-text-white);
      transform: scale(0.95);
    }
  }

  .input-hint {
    font-size: $app-font-size-sm;
    color: var(--color-text-muted);
    text-align: center;
    line-height: 1.4;
  }
}

.modal-footer {
  padding: 0 $app-spacing-md $app-spacing-lg;
}

.save-button {
  width: 100%;
  height: 40px;
  background-color: var(--color-primary);
  color: var(--color-text-white);
  border: none;
  border-radius: $app-card-radius;
  font-size: $app-font-size-base;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--color-primary-press);
  }

  &:active {
    background-color: var(--color-primary-press);
    transform: scale(0.98);
  }
}
</style>
