<template>
  <view v-if="innerVisible" class="cm-root" :style="{ zIndex: zIndex }">
    <view
      v-if="mask"
      class="cm-mask"
      :class="animation ? (isEntering ? 'cm-fade-in' : 'cm-fade-out') : ''"
      :catchtouchmove="lockScroll"
      @touchmove.stop.prevent
      @click="onMaskClick"
    />

    <view
      class="cm-container"
      :class="[
        position === 'bottom' ? 'is-bottom' : 'is-center',
        animation ? (isEntering ? (position === 'bottom' ? 'cm-slide-in' : 'cm-zoom-in') : (position === 'bottom' ? 'cm-slide-out' : 'cm-zoom-out')) : ''
      ]"
      :style="{ zIndex: zIndex + 1 }"
      @click.stop
    >
      <view class="cm-panel card" :class="position === 'bottom' ? 'cm-panel-bottom' : 'cm-panel-center'">
        <!-- Header -->
        <template v-if="!hasHeaderSlot && (title || showClose)">
          <view class="cm-header cm-header--sm">
            <text class="cm-title cm-title--sm">{{ title }}</text>
            <view v-if="showClose" class="cm-close cm-close--sm" @click="handleCancel">×</view>
          </view>
        </template>
        <slot name="header" v-else />

        <!-- Content -->
        <view class="cm-content cm-content--sm">
          <slot />
        </view>

        <!-- Footer -->
        <template v-if="!hasFooterSlot && showFooter">
          <view class="cm-footer">
            <button class="btn btn--sm" @click="handleCancel">{{ cancelText }}</button>
            <button class="btn btn--primary btn--sm" @click="handleConfirm">{{ confirmText }}</button>
          </view>
        </template>
        <slot name="footer" v-else />
      </view>
    </view>
  </view>
  
</template>

<script setup>
import { ref, watch, nextTick, computed, useSlots } from 'vue'

const props = defineProps({
  visible: { type: Boolean, default: false },
  position: { type: String, default: 'center' }, // 'center' | 'bottom'
  mask: { type: Boolean, default: true },
  maskClosable: { type: Boolean, default: true },
  animation: { type: Boolean, default: true },
  zIndex: { type: Number, default: 1000 },
  lockScroll: { type: Boolean, default: true },
  title: { type: String, default: '' },
  showClose: { type: Boolean, default: true },
  confirmText: { type: String, default: '确定' },
  cancelText: { type: String, default: '取消' },
  showFooter: { type: Boolean, default: true },
})

const emit = defineEmits([
  'update:visible',
  'open',
  'close',
  'maskClick',
  'confirm',
  'cancel',
])

const slots = useSlots()
const hasHeaderSlot = computed(() => !!slots.header)
const hasFooterSlot = computed(() => !!slots.footer)

const innerVisible = ref(false)
const isEntering = ref(false)

const open = async () => {
  if (innerVisible.value) return
  innerVisible.value = true
  await nextTick()
  if (props.animation) {
    isEntering.value = true
  }
  emit('update:visible', true)
  emit('open')
}

const close = () => {
  if (!innerVisible.value) return
  if (props.animation) {
    isEntering.value = false
    setTimeout(() => {
      innerVisible.value = false
      emit('update:visible', false)
      emit('close')
    }, 200)
  } else {
    innerVisible.value = false
    emit('update:visible', false)
    emit('close')
  }
}

const onMaskClick = () => {
  emit('maskClick')
  if (props.maskClosable) {
    close()
  }
}

const handleConfirm = () => {
  emit('confirm')
  close()
}

const handleCancel = () => {
  emit('cancel')
  close()
}

watch(
  () => props.visible,
  (val) => {
    if (val) {
      open()
    } else {
      close()
    }
  },
  { immediate: false }
)

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
@import '../uni.scss';

.cm-root {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.cm-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: var(--color-overlay);
}

.cm-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
}

.is-center {
  align-items: center;
  justify-content: center;
}

.is-bottom {
  align-items: flex-end;
  justify-content: center;
}

.cm-panel {
  background-color: var(--color-bg-card);
  box-shadow: var(--shadow-lg);
  border-radius: $app-card-radius;
  max-width: 92vw;
  box-sizing: border-box;
  overflow-x: hidden;
}

.cm-panel-center {
  max-height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.cm-panel-bottom {
  width: 100vw;
  border-radius: $app-card-radius $app-card-radius 0 0;
  max-height: 80vh;
  overflow-y: auto;
}

.cm-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $app-spacing-lg;
  border-bottom: 1px solid var(--color-border);
}
.cm-header--sm { padding: $app-spacing-md; }

.cm-title {
  font-size: $app-font-size-xl;
  font-weight: bold;
  color: var(--color-text-primary);
}
.cm-title--sm { font-size: $app-font-size-lg; }

.cm-close {
  font-size: 48rpx;
  color: var(--color-text-muted);
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cm-close--sm { font-size: 36rpx; width: 48rpx; height: 48rpx; }

.cm-content {
  padding: $app-spacing-lg;
}
.cm-content--sm { padding: $app-spacing-md; }

.cm-footer {
  display: flex;
  gap: $app-spacing-md;
  padding: $app-spacing-lg;
  border-top: 1px solid var(--color-border);
}

/* Animations */
.cm-fade-in {
  animation: cmFadeIn 0.2s ease-out;
}

.cm-fade-out {
  animation: cmFadeOut 0.2s ease-in;
}

.cm-zoom-in {
  animation: cmZoomIn 0.2s ease-out;
}

.cm-zoom-out {
  animation: cmZoomOut 0.2s ease-in;
}

.cm-slide-in {
  animation: cmSlideIn 0.2s ease-out;
}

.cm-slide-out {
  animation: cmSlideOut 0.2s ease-in;
}

@keyframes cmFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes cmFadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes cmZoomIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes cmZoomOut {
  from { opacity: 1; transform: scale(1); }
  to { opacity: 0; transform: scale(0.95); }
}

@keyframes cmSlideIn {
  from { opacity: 0; transform: translateY(32rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes cmSlideOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(32rpx); }
}
</style>


