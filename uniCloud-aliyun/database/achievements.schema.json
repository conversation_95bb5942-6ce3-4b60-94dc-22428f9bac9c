{"bsonType": "object", "description": "用户成就解锁状态（去重存储、含已读状态）", "required": ["wx_openid", "achievements"], "properties": {"_id": {"description": "系统ID"}, "wx_openid": {"bsonType": "string", "description": "微信openId（唯一）"}, "achievements": {"bsonType": "array", "description": "成就解锁数组", "items": {"bsonType": "object", "required": ["id", "unlockedAt"], "properties": {"id": {"bsonType": "string", "description": "成就ID（catalog内定义）"}, "unlockedAt": {"bsonType": "long", "description": "解锁时间戳(ms)"}, "read": {"bsonType": "bool", "description": "是否已读", "default": false}, "readAt": {"bsonType": "long", "description": "已读时间戳(ms)"}}}}, "updatedAt": {"bsonType": "long", "description": "最近更新时间(ms)"}}, "permission": {"read": false, "create": false, "update": false, "delete": false, "count": false}}