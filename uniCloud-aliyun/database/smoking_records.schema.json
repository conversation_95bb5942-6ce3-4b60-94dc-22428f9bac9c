{"bsonType": "object", "description": "单用户按月分片的吸烟记录表（文档内按天分组）", "required": ["wx_openid", "yearMonth", "records"], "properties": {"yearMonth": {"bsonType": "string", "description": "年月分片，格式：YYYY-MM", "title": "Year-Month"}, "_id": {"description": "系统自动生成ID"}, "wx_openid": {"bsonType": "string", "description": "微信openId（唯一）", "title": "微信OpenID"}, "records": {"bsonType": "array", "description": "每日分组记录数组", "items": {"bsonType": "object", "required": ["date", "totals", "items"], "properties": {"date": {"bsonType": "string", "description": "YYYY-MM-DD"}, "totals": {"bsonType": "object", "properties": {"cigarettes": {"bsonType": "int", "minimum": 0, "default": 0}, "puffs": {"bsonType": "int", "minimum": 0, "default": 0}, "spent": {"bsonType": "double", "minimum": 0, "default": 0}}}, "items": {"bsonType": "array", "items": {"bsonType": "object", "required": ["id", "box_id", "type", "amount", "timestamp"], "properties": {"id": {"bsonType": "string"}, "box_id": {"bsonType": "string"}, "type": {"bsonType": "string", "enum": ["cigarette", "electronic"]}, "amount": {"bsonType": "int", "minimum": 0}, "note": {"bsonType": "string"}, "timestamp": {"bsonType": "long"}, "fake": {"bsonType": "bool", "description": "是否为云吸(虚拟)记录", "default": false}, "meta": {"bsonType": "object", "description": "附加元信息", "properties": {"virtual": {"bsonType": "bool"}, "brandId": {"bsonType": "string"}, "brandName": {"bsonType": "string"}, "brandType": {"bsonType": "string", "enum": ["cigarette", "electronic"]}, "specs": {"bsonType": "object"}}}}}}}}}}, "permission": {"read": false, "create": false, "update": false, "delete": false, "count": false}}