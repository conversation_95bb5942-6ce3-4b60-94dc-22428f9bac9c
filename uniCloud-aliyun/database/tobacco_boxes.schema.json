{"bsonType": "object", "description": "用户烟盒表（活跃/历史）", "required": ["wx_openid", "brand_id", "type", "price", "start_date"], "properties": {"_id": {"description": "系统自动生成ID"}, "wx_openid": {"bsonType": "string", "description": "微信openId", "title": "微信OpenID"}, "brand_id": {"bsonType": "string", "description": "品牌ID（关联brands表）"}, "type": {"bsonType": "string", "enum": ["cigarette", "electronic"], "description": "烟草类型"}, "price": {"bsonType": "double", "minimum": 0, "description": "该盒价格"}, "nickname": {"bsonType": "string", "description": "盒子昵称"}, "specs": {"bsonType": "object", "description": "规格信息：按type区分结构", "properties": {"cigarette": {"bsonType": "object", "properties": {"total": {"bsonType": "int", "minimum": 1}, "consumed": {"bsonType": "int", "minimum": 0}}}, "electronic": {"bsonType": "object", "properties": {"totalCartridges": {"bsonType": "int", "minimum": 1}, "currentCartridge": {"bsonType": "int", "minimum": 1}, "cartridgePuffsLimit": {"bsonType": "int", "minimum": 1}, "currentCartridgePuffs": {"bsonType": "int", "minimum": 0}}}}}, "is_completed": {"bsonType": "bool", "default": false}, "start_date": {"bsonType": "string", "description": "YYYY-MM-DD"}, "last_used_time": {"bsonType": "timestamp", "description": "最后使用时间"}, "create_time": {"bsonType": "timestamp", "forceDefaultValue": {"$env": "now"}}, "update_time": {"bsonType": "timestamp", "forceDefaultValue": {"$env": "now"}}}, "permission": {"read": false, "create": false, "update": false, "delete": false, "count": false}}