{"bsonType": "object", "description": "品牌表（预设 + 用户自定义）", "required": ["name", "type", "default_price", "default_specs"], "properties": {"_id": {"description": "系统自动生成ID"}, "name": {"bsonType": "string", "minLength": 1, "maxLength": 40}, "type": {"bsonType": "string", "enum": ["cigarette", "electronic"]}, "default_price": {"bsonType": "double", "minimum": 0}, "image": {"bsonType": "string"}, "is_virtual": {"bsonType": "bool", "default": false}, "is_custom": {"bsonType": "bool", "default": false}, "wx_openid": {"bsonType": "string", "description": "自定义品牌归属用户，预设为空"}, "default_specs": {"bsonType": "object", "properties": {"cigarette": {"bsonType": "object", "properties": {"count": {"bsonType": "int", "minimum": 1}}}, "electronic": {"bsonType": "object", "properties": {"cartridges": {"bsonType": "int", "minimum": 1}, "puffsPerCartridge": {"bsonType": "int", "minimum": 1}}}}}, "personal_rating": {"bsonType": "int", "description": "个人评分 0-5", "minimum": 0, "maximum": 5, "default": 0}, "personal_notes": {"bsonType": "string", "description": "个人备注（最多500字）", "maxLength": 500, "default": ""}, "create_time": {"bsonType": "timestamp", "forceDefaultValue": {"$env": "now"}}, "update_time": {"bsonType": "timestamp", "forceDefaultValue": {"$env": "now"}}}, "permission": {"read": false, "create": false, "update": false, "delete": false, "count": false}}