{"bsonType": "object", "description": "轻清戒烟日记用户表", "required": ["wx_openid", "nickname"], "properties": {"_id": {"description": "用户ID，系统自动生成"}, "wx_openid": {"bsonType": "string", "description": "微信小程序openid，用户唯一标识", "pattern": "^[a-zA-Z0-9_-]{28}$", "title": "微信OpenID"}, "wx_unionid": {"bsonType": "string", "description": "微信unionid，跨应用用户识别", "title": "微信UnionID"}, "nickname": {"bsonType": "string", "description": "用户昵称，可修改", "minLength": 1, "maxLength": 20, "title": "用户昵称"}, "avatar_fileId": {"bsonType": "string", "description": "用户头像云存储文件ID", "title": "头像文件ID"}, "avatar_url": {"bsonType": "string", "description": "用户头像URL地址", "title": "头像URL"}, "gender": {"bsonType": "int", "description": "性别：0未知，1男，2女", "minimum": 0, "maximum": 2, "default": 0, "title": "性别"}, "birth_date": {"bsonType": "string", "description": "出生日期，格式：YYYY-MM-DD", "title": "出生日期"}, "phone": {"bsonType": "string", "description": "手机号码", "title": "手机号码"}, "email": {"bsonType": "string", "description": "邮箱地址", "title": "邮箱地址"}, "total_cigarettes": {"bsonType": "int", "description": "总抽烟根数", "minimum": 0, "default": 0, "title": "总抽烟根数"}, "total_puffs": {"bsonType": "int", "description": "总电子烟口数", "minimum": 0, "default": 0, "title": "总电子烟口数"}, "total_spent": {"bsonType": "double", "description": "总花费金额", "minimum": 0, "default": 0, "title": "总花费"}, "total_days": {"bsonType": "int", "description": "总记录天数", "minimum": 0, "default": 0, "title": "总记录天数"}, "user_level": {"bsonType": "int", "description": "用户等级", "minimum": 1, "default": 1, "title": "用户等级"}, "status": {"bsonType": "int", "description": "账户状态：0正常，1禁用", "enum": [0, 1], "default": 0, "title": "账户状态"}, "register_time": {"bsonType": "timestamp", "description": "注册时间", "forceDefaultValue": {"$env": "now"}, "title": "注册时间"}, "last_login_time": {"bsonType": "timestamp", "description": "最后登录时间", "title": "最后登录时间"}, "last_login_ip": {"bsonType": "string", "description": "最后登录IP地址", "title": "最后登录IP"}, "login_count": {"bsonType": "int", "description": "登录次数", "minimum": 0, "default": 0, "title": "登录次数"}, "preferences": {"bsonType": "object", "description": "用户偏好设置", "title": "偏好设置", "properties": {"theme": {"bsonType": "string", "description": "主题设置：light, dark", "enum": ["light", "dark"], "default": "light"}, "reminder_enabled": {"bsonType": "bool", "description": "是否启用提醒", "default": true}, "daily_limit": {"bsonType": "object", "description": "每日限制设置", "properties": {"enabled": {"bsonType": "bool", "default": false}, "cigarettes": {"bsonType": "int", "minimum": 1, "default": 10}, "puffs": {"bsonType": "int", "minimum": 1, "default": 100}}}, "max_active_boxes": {"bsonType": "int", "description": "最大活跃烟盒数量", "minimum": 1, "maximum": 10, "default": 3}, "default_cigarette_count": {"bsonType": "int", "description": "每盒卷烟默认根数", "minimum": 1, "default": 20}, "default_electronic_puffs": {"bsonType": "int", "description": "电子烟每颗烟弹默认口数", "minimum": 1, "default": 300}, "haptics_enabled": {"bsonType": "bool", "description": "是否启用震动反馈", "default": true}, "quick_record_presets": {"bsonType": "object", "description": "快速记录预设值", "properties": {"cigarette": {"bsonType": "array", "items": {"bsonType": "int", "minimum": 1}, "description": "卷烟快速记录预设", "default": [1, 2, 3]}, "electronic": {"bsonType": "array", "items": {"bsonType": "int", "minimum": 1}, "description": "电子烟快速记录预设", "default": [5, 10, 20]}}}, "auto_suggest_presets": {"bsonType": "bool", "description": "是否根据近7天频次自动优化预设排序", "default": true}}}}, "permission": {"read": "doc._id == auth.uid", "create": false, "update": "doc._id == auth.uid && Object.keys(data).every(key => ['nickname', 'avatar_fileId', 'avatar_url', 'gender', 'birth_date', 'phone', 'email', 'preferences'].includes(key))", "delete": false, "count": false}}