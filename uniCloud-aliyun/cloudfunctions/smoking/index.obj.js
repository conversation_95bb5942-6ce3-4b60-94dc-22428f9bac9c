const { verifyToken } = require("./jwt-utils");

async function getOpenIdAndUser(token, usersCollection) {
  const verifyResult = verifyToken(token);
  if (!verifyResult.valid) {
    return { ok: false, code: 401, message: "Token无效，请重新登录" };
  }
  const openId = verifyResult.payload.openId;
  const userRes = await usersCollection.where({ wx_openid: openId }).get();
  if (!userRes || userRes.data.length === 0) {
    return { ok: false, code: 404, message: "用户不存在" };
  }
  return { ok: true, openId, user: userRes.data[0] };
}

function toEpochMs(value) {
  try {
    if (!value) return Date.now();
    const t = new Date(value).getTime();
    return Number.isFinite(t) ? t : Date.now();
  } catch (_) {
    return Date.now();
  }
}

function formatDateYYYYMMDD(dateLike) {
  const d = new Date(dateLike || Date.now());
  const y = d.getFullYear();
  const m = String(d.getMonth() + 1).padStart(2, '0');
  const da = String(d.getDate()).padStart(2, '0');
  return `${y}-${m}-${da}`;
}

function formatYearMonth(dateLike) {
  const d = new Date(dateLike || Date.now());
  const y = d.getFullYear();
  const m = String(d.getMonth() + 1).padStart(2, '0');
  return `${y}-${m}`;
}

function getYearMonthRange(startDate, endDate) {
  if (!startDate || !endDate) return null;
  try {
    const s = new Date(String(startDate).replace(/-/g, '/'));
    const e = new Date(String(endDate).replace(/-/g, '/'));
    if (Number.isNaN(s.getTime()) || Number.isNaN(e.getTime())) return null;
    let from = new Date(s.getFullYear(), s.getMonth(), 1);
    let to = new Date(e.getFullYear(), e.getMonth(), 1);
    if (from > to) { const tmp = from; from = to; to = tmp; }
    const out = [];
    for (let d = new Date(from); d <= to; d = new Date(d.getFullYear(), d.getMonth() + 1, 1)) {
      out.push(formatYearMonth(d));
      if (out.length > 240) break; // 安全阈值：最多20年
    }
    return out;
  } catch (_) { return null; }
}

// 兼容 uniCloud 不同返回形态的 doc.get() 结果：{ data: [obj] } 或 { data: obj }
function normalizeGetDoc(getRes) {
  try {
    if (!getRes) return null;
    const d = getRes.data;
    if (Array.isArray(d)) return d[0] || null;
    return d || null;
  } catch (_) { return null; }
}

module.exports = {
  _before() {
    this.db = uniCloud.database();
    this.users = this.db.collection('users');
    this.brands = this.db.collection('brands');
    this.boxes = this.db.collection('tobacco_boxes');
    this.records = this.db.collection('smoking_records');
    this.cmd = this.db.command;
  },

  // ============ 品牌 ============
  async getBrands(token, options = {}) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };

      // 仅返回当前用户自建品牌
      const mineRes = await this.brands.where({ wx_openid: auth.openId }).get();
      const list = (mineRes.data || []).map(b => ({
        id: b._id,
        name: b.name,
        type: b.type,
        defaultPrice: b.default_price,
        image: b.image,
        isCustom: !!b.is_custom,
        isVirtual: !!b.is_virtual,
        defaultSpecs: b.default_specs || {},
        personalRating: typeof b.personal_rating === 'number' ? b.personal_rating : 0,
        personalNotes: typeof b.personal_notes === 'string' ? b.personal_notes : ''
      }));
      return { code: 200, data: list };
    } catch (e) {
      console.error('getBrands error', e);
      return { code: 500, message: '获取品牌失败' };
    }
  },

  async addCustomBrand(token, payload) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      const { name, type, defaultPrice, image, defaultSpecs } = payload || {};
      if (!name || !type || typeof defaultPrice !== 'number') {
        return { code: 400, message: '参数错误' };
      }
      const doc = {
        name,
        type,
        default_price: defaultPrice,
        image: image || '',
        is_custom: true,
        wx_openid: auth.openId,
        default_specs: defaultSpecs || {}
      };
      const r = await this.brands.add(doc);
      return { code: 200, data: { id: r.id, ...payload } };
    } catch (e) {
      console.error('addCustomBrand error', e);
      return { code: 500, message: '添加自定义品牌失败' };
    }
  },

  async updateBrand(token, brandId, patch) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      if (!brandId || !patch || typeof patch !== 'object') return { code: 400, message: '参数错误' };

      const res = await this.brands.doc(brandId).get();
      if (!res.data || res.data.length === 0) return { code: 404, message: '品牌不存在' };
      const brand = res.data[0];
      if (!brand.is_custom || brand.wx_openid !== auth.openId) return { code: 403, message: '无权操作此品牌' };

      // 不允许修改的字段
      const forbidden = new Set(['type', 'is_custom', 'wx_openid', '_id', 'create_time', 'update_time']);
      for (const k of Object.keys(patch)) {
        if (forbidden.has(k)) return { code: 400, message: `不允许修改字段: ${k}` };
      }

      // 构建更新数据（字段名映射）
      const updateData = { update_time: new Date() };
      if (patch.name !== undefined) updateData.name = patch.name;
      if (patch.defaultPrice !== undefined) {
        if (typeof patch.defaultPrice !== 'number' || !(patch.defaultPrice >= 0)) return { code: 400, message: 'defaultPrice 参数错误' };
        updateData.default_price = patch.defaultPrice;
      }
      if (patch.image !== undefined) updateData.image = patch.image;
      if (patch.defaultSpecs !== undefined) updateData.default_specs = patch.defaultSpecs || {};

      // 个人评分与备注
      if (patch.personalRating !== undefined) {
        const r = Number(patch.personalRating);
        if (!Number.isInteger(r) || r < 0 || r > 5) return { code: 400, message: 'personalRating 必须是 0~5 的整数' };
        updateData.personal_rating = r;
      }
      if (patch.personalNotes !== undefined) {
        const n = String(patch.personalNotes);
        if (n.length > 500) return { code: 400, message: 'personalNotes 超过长度限制(≤500)' };
        updateData.personal_notes = n;
      }

      await this.brands.doc(brandId).update(updateData);
      return { code: 200, message: '更新成功' };
    } catch (e) {
      console.error('updateBrand error', e);
      return { code: 500, message: '更新品牌失败' };
    }
  },

  async removeBrand(token, brandId) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      if (!brandId) return { code: 400, message: 'brandId必填' };

      const res = await this.brands.doc(brandId).get();
      if (!res.data || res.data.length === 0) return { code: 404, message: '品牌不存在' };
      const brand = res.data[0];
      if (!brand.is_custom || brand.wx_openid !== auth.openId) return { code: 403, message: '无权操作此品牌' };

      // 检查是否被任意烟盒引用（活跃或历史均算）
      const cnt = await this.boxes.where({ brand_id: brandId }).count();
      if ((cnt.total || 0) > 0) return { code: 400, message: '该品牌已被使用，无法删除' };

      await this.brands.doc(brandId).remove();
      return { code: 200, message: '删除成功' };
    } catch (e) {
      console.error('removeBrand error', e);
      return { code: 500, message: '删除品牌失败' };
    }
  },

  // ============ 烟盒 ============
  async listBoxes(token, { activeOnly = false } = {}) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      const where = activeOnly ? { wx_openid: auth.openId, is_completed: false } : { wx_openid: auth.openId };
      const res = await this.boxes.where(where).orderBy('last_used_time', 'desc').get();
      const list = (res.data || []).map(b => ({
        id: b._id,
        brandId: b.brand_id,
        type: b.type,
        price: b.price,
        nickname: b.nickname || '',
        specs: b.specs || {},
        isCompleted: !!b.is_completed,
        startDate: b.start_date,
        lastUsedDate: toEpochMs(b.last_used_time)
      }));
      return { code: 200, data: list };
    } catch (e) {
      console.error('listBoxes error', e);
      return { code: 500, message: '获取烟盒列表失败' };
    }
  },

  async createBox(token, payload) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      const { brandId, type, price, nickname, specs } = payload || {};
      if (!brandId || !type || typeof price !== 'number' || !specs) {
        return { code: 400, message: '参数错误' };
      }
      // 校验品牌归属
      const brandRes = await this.brands.doc(brandId).get();
      if (!brandRes.data || brandRes.data.length === 0) return { code: 404, message: '品牌不存在' };
      const brand = brandRes.data[0];
      if (brand.wx_openid !== auth.openId) return { code: 403, message: '无权使用该品牌' };
      const now = new Date();
      const doc = {
        wx_openid: auth.openId,
        brand_id: brandId,
        type,
        price,
        nickname: nickname || '',
        specs: specs || {},
        is_completed: false,
        start_date: formatDateYYYYMMDD(now),
        last_used_time: now,
        create_time: now,
        update_time: now
      };
      const r = await this.boxes.add(doc);
      return {
        code: 200,
        data: {
          id: r.id,
          brandId,
          type,
          price,
          nickname: nickname || '',
          specs: specs || {},
          isCompleted: false,
          startDate: doc.start_date,
          lastUsedDate: now.getTime()
        }
      };
    } catch (e) {
      console.error('createBox error', e);
      return { code: 500, message: '创建烟盒失败' };
    }
  },

  async updateBox(token, boxId, patch) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      const res = await this.boxes.doc(boxId).get();
      if (!res.data || res.data.length === 0) return { code: 404, message: '烟盒不存在' };
      const box = res.data[0];
      if (box.wx_openid !== auth.openId) return { code: 403, message: '无权操作' };
      const updateData = { ...patch, update_time: new Date() };
      await this.boxes.doc(boxId).update(updateData);
      return { code: 200, message: '更新成功' };
    } catch (e) {
      console.error('updateBox error', e);
      return { code: 500, message: '更新烟盒失败' };
    }
  },

  async completeBox(token, boxId) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      const res = await this.boxes.doc(boxId).get();
      if (!res.data || res.data.length === 0) return { code: 404, message: '烟盒不存在' };
      const box = res.data[0];
      if (box.wx_openid !== auth.openId) return { code: 403, message: '无权操作' };
      if (box.is_completed) return { code: 400, message: '该烟盒已完成' };

      // 仅允许卷烟通过 completeBox 完成并计费；电子烟请使用 completeCartridge
      if (box.type !== 'cigarette') {
        return { code: 400, message: '仅卷烟支持直接完成盒子' };
      }

      const now = new Date();
      const price = Number(box.price) || 0;

      // 写入一条“完成计费”记录（amount=0，仅计费）到当月文档
      const date = formatDateYYYYMMDD(now);
      const ym = formatYearMonth(now);
      const recId = `${Date.now()}_${Math.random().toString(16).slice(2, 8)}`;
      const recItem = { id: recId, box_id: boxId, type: 'cigarette', amount: 0, note: '', timestamp: now.getTime(), spent: Math.round(price * 100) / 100, meta: { type: 'cigarette', complete: true } };
      let days = [];
      let recDocId = null;
      const monthRes = await this.records.where({ wx_openid: auth.openId, yearMonth: ym }).get();
      if (monthRes && monthRes.data && monthRes.data.length > 0) {
        recDocId = monthRes.data[0]._id;
        days = monthRes.data[0].records || [];
      } else {
        const addRes = await this.records.add({ wx_openid: auth.openId, yearMonth: ym, records: [] });
        recDocId = addRes.id;
      }

      let day = days.find(d => d.date === date);
      if (!day) {
        day = { date, totals: { cigarettes: 0, puffs: 0, spent: 0 }, items: [] };
        days.push(day);
      }
      (day.items || (day.items = [])).unshift(recItem);
      day.totals.spent = Math.round(((day.totals.spent || 0) + (recItem.spent || 0)) * 100) / 100;
      await this.records.doc(recDocId).update({ records: days });

      // 标记盒子完成
      await this.boxes.doc(boxId).update({ is_completed: true, last_used_time: now, update_time: now });

      // 更新用户累计费用
      await this.users.doc(auth.user._id).update({ total_spent: Math.round(((auth.user.total_spent || 0) + (recItem.spent || 0)) * 100) / 100 });

      return { code: 200, data: { recordId: recId, timestamp: recItem.timestamp, date, spent: recItem.spent, type: 'cigarette' } };
    } catch (e) {
      console.error('completeBox error', e);
      return { code: 500, message: '完成烟盒失败' };
    }
  },

  async removeBox(token, boxId) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      const res = await this.boxes.doc(boxId).get();
      if (!res.data || res.data.length === 0) return { code: 404, message: '烟盒不存在' };
      const box = res.data[0];
      if (box.wx_openid !== auth.openId) return { code: 403, message: '无权操作' };
      await this.boxes.doc(boxId).remove();
      return { code: 200, message: '删除成功' };
    } catch (e) {
      console.error('removeBox error', e);
      return { code: 500, message: '删除烟盒失败' };
    }
  },

  // ============ 记录 ============
  async addRecord(token, { boxId, type, amount, note = '' }) {
    // 事务限制兼容：先在事务外获取必要标识
    const auth = await getOpenIdAndUser(token, this.users);
    if (!auth.ok) return { code: auth.code, message: auth.message };
    // 预取当月文档ID，避免事务内 find
    const ym = formatYearMonth(Date.now());
    let preRecDocId = null;
    try {
      const pre = await this.records.where({ wx_openid: auth.openId, yearMonth: ym }).get();
      if (pre && pre.data && pre.data.length > 0) preRecDocId = pre.data[0]._id;
    } catch (_) {}
    const transaction = await this.db.startTransaction();
    try {
      // 读取并更新盒子
      const boxRes = await transaction.collection('tobacco_boxes').doc(boxId).get();
      const boxData = Array.isArray(boxRes.data) ? (boxRes.data[0] || null) : (boxRes.data || null);
      if (!boxData) { await transaction.rollback(); return { code: 404, message: '烟盒不存在' }; }
      const box = boxData;
      if (box.wx_openid !== auth.openId) { await transaction.rollback(); return { code: 403, message: '无权操作' }; }
      if (type !== box.type) { await transaction.rollback(); return { code: 400, message: '记录类型与烟盒不匹配' }; }
      if (!(amount > 0)) { await transaction.rollback(); return { code: 400, message: '数量必须大于0' }; }

      // 更新盒子进度
      const now = new Date();
      const newSpecs = JSON.parse(JSON.stringify(box.specs || {}));
      let actualAmount = amount;
      let autoCompleted = false;
      let completionRec = null;
      if (box.type === 'cigarette') {
        newSpecs.cigarette = newSpecs.cigarette || {};
        const total = Number(newSpecs.cigarette.total || box.specs?.cigarette?.total || 20);
        const consumedBefore = Number(newSpecs.cigarette.consumed || 0);
        const remaining = Math.max(0, total - consumedBefore);
        actualAmount = Math.max(0, Math.min(amount, remaining));
        newSpecs.cigarette.consumed = consumedBefore + actualAmount;
        // 到达或达到上限自动完成，并按盒计费
        if (total > 0 && newSpecs.cigarette.consumed >= total) {
          autoCompleted = true;
        }
      } else {
        // electronic: 不自动进位，仅累计口数；允许超过每颗上限
        newSpecs.electronic = newSpecs.electronic || {};
        const currentCartridge = newSpecs.electronic.currentCartridge || 1;
        const currentPuffs = (newSpecs.electronic.currentCartridgePuffs || 0) + amount;
        newSpecs.electronic.currentCartridge = currentCartridge;
        newSpecs.electronic.currentCartridgePuffs = Math.max(0, currentPuffs);
      }
      // 更新盒子（若自动完成则一并标记完成）
      const boxUpdateData = { specs: newSpecs, last_used_time: now, update_time: now };
      if (autoCompleted) boxUpdateData.is_completed = true;
      await transaction.collection('tobacco_boxes').doc(boxId).update(boxUpdateData);

      // 费用计算（电子烟按颗计费）
      let spent = 0;
      let recMeta = { type };
      if (box.type === 'cigarette') {
        // 卷烟按盒计费：平时记录不计费，仅完成时计费
        spent = 0;
      } else {
        // electronic: 平时记录不计费，费用在手动完成时结算
        spent = 0;
        recMeta = { type };
      }

      // 写入聚合记录（按月分片）
      const date = formatDateYYYYMMDD(now);
      const recId = `${Date.now()}_${Math.random().toString(16).slice(2, 8)}`;
      const recItem = { id: recId, box_id: boxId, type, amount: actualAmount, note, timestamp: now.getTime(), spent, meta: recMeta };
      // 获取/创建当月文档
      let recDocId = preRecDocId || null;
      let days = [];
      if (!recDocId) {
        const addRes = await transaction.collection('smoking_records').add({ wx_openid: auth.openId, yearMonth: ym, records: [] });
        recDocId = addRes.id;
      } else {
        const docRes = await transaction.collection('smoking_records').doc(recDocId).get();
        const docObj = Array.isArray(docRes.data) ? (docRes.data[0] || {}) : (docRes.data || {});
        days = docObj.records || [];
      }

      let day = days.find(d => d.date === date);
      let isNewDay = false;
      if (!day) {
        day = { date, totals: { cigarettes: 0, puffs: 0, spent: 0 }, items: [] };
        days.push(day);
        isNewDay = true;
      }
      (day.items || (day.items = [])).unshift(recItem);
      if (type === 'cigarette') day.totals.cigarettes += actualAmount; else day.totals.puffs += actualAmount;
      day.totals.spent = Math.round(((day.totals.spent || 0) + (spent || 0)) * 100) / 100;

      // 若卷烟达到上限则追加一条“完成计费”记录
      if (autoCompleted && box.type === 'cigarette') {
        const price = Number(box.price) || 0;
        const completeId = `${Date.now()}_${Math.random().toString(16).slice(2, 8)}`;
        completionRec = { id: completeId, box_id: boxId, type: 'cigarette', amount: 0, note: '', timestamp: now.getTime(), spent: Math.round(price * 100) / 100, meta: { type: 'cigarette', complete: true } };
        (day.items || (day.items = [])).unshift(completionRec);
        day.totals.spent = Math.round(((day.totals.spent || 0) + (completionRec.spent || 0)) * 100) / 100;
      }

      await transaction.collection('smoking_records').doc(recDocId).update({ records: days });

      // 更新用户总计：按 doc(_id) 更新
      const userPatch = {};
      if (type === 'cigarette') userPatch.total_cigarettes = (auth.user.total_cigarettes || 0) + actualAmount;
      else userPatch.total_puffs = (auth.user.total_puffs || 0) + actualAmount;
      let addedSpent = spent || 0;
      if (completionRec) addedSpent += (completionRec.spent || 0);
      userPatch.total_spent = Math.round(((auth.user.total_spent || 0) + addedSpent) * 100) / 100;
      if (isNewDay) userPatch.total_days = (auth.user.total_days || 0) + 1;
      await transaction.collection('users').doc(auth.user._id).update(userPatch);

      await transaction.commit();
      return { code: 200, data: { recordId: recId, timestamp: recItem.timestamp, date, spent, amount: recItem.amount, completed: !!completionRec, completionRecord: completionRec ? { recordId: completionRec.id, timestamp: completionRec.timestamp, spent: completionRec.spent } : null } };
    } catch (e) {
      console.error('addRecord error', e);
      try { await transaction.rollback(); } catch (_) {}
      return { code: 500, message: e && e.message ? e.message : '新增记录失败' };
    }
  },

  async undoRecord(token, { recordId }) {
    const transaction = await this.db.startTransaction();
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) { await transaction.rollback(); return { code: auth.code, message: auth.message }; }
      if (!recordId) { await transaction.rollback(); return { code: 400, message: 'recordId必填' }; }

      // 跨所有月份扫描目标记录（改为事务外预取ID列表以规避事务 find）
      const preAll = await this.records.where({ wx_openid: auth.openId }).get();
      const ids = (preAll.data || []).map(d => d._id);
      // 事务内逐份读取 doc
      const docs = [];
      for (const id of ids) {
        const dr = await transaction.collection('smoking_records').doc(id).get();
        const docObj = normalizeGetDoc(dr);
        if (docObj) docs.push(docObj);
      }
      const rRes = { data: docs };
      if (!rRes.data || rRes.data.length === 0) { await transaction.rollback(); return { code: 404, message: '没有可撤销的记录' }; }
      let recDoc = null;
      let days = [];
      for (const d of rRes.data) {
        const rds = d.records || [];
        let hit = false;
        for (const one of rds) {
          const arr = one.items || one.records || [];
          if (arr.find(x => x.id === recordId)) { hit = true; break; }
        }
        if (hit) { recDoc = d; days = rds; break; }
      }
      if (!recDoc) { await transaction.rollback(); return { code: 404, message: '记录不存在' }; }
      let foundDayIndex = -1, foundIndex = -1, found, day;
      for (let i = 0; i < days.length; i++) {
        const arr = days[i].items || days[i].records || [];
        const idx = arr.findIndex(r => r.id === recordId);
        if (idx !== -1) {
          foundDayIndex = i; foundIndex = idx; day = days[i]; found = arr[idx]; break;
        }
      }
      if (!found) { await transaction.rollback(); return { code: 404, message: '记录不存在' }; }

      // 回滚盒子
      const boxRes = await transaction.collection('tobacco_boxes').doc(found.box_id).get();
      const box = normalizeGetDoc(boxRes);
      if (box) {
        if (box.wx_openid !== auth.openId) { await transaction.rollback(); return { code: 403, message: '无权操作' }; }
        const newSpecs = JSON.parse(JSON.stringify(box.specs || {}));
        if (box.type === 'cigarette') {
          const next = (newSpecs.cigarette?.consumed || 0) - (found.amount || 0);
          newSpecs.cigarette = newSpecs.cigarette || {};
          newSpecs.cigarette.consumed = Math.max(0, next);
        } else {
          // electronic: 不跨颗回滚，仅回退当前口数
          newSpecs.electronic = newSpecs.electronic || {};
          const next = (newSpecs.electronic.currentCartridgePuffs || 0) - (found.amount || 0);
          newSpecs.electronic.currentCartridgePuffs = Math.max(0, next);
          newSpecs.electronic.currentCartridge = Math.max(1, newSpecs.electronic.currentCartridge || 1);
        }
        await transaction.collection('tobacco_boxes').doc(found.box_id).update({ specs: newSpecs, update_time: new Date() });
      }

      // 回滚 totals 并删除记录
      const arr = day.items || day.records || [];
      arr.splice(foundIndex, 1);
      if (found.type === 'cigarette') day.totals.cigarettes = Math.max(0, (day.totals.cigarettes || 0) - (found.amount || 0));
      else day.totals.puffs = Math.max(0, (day.totals.puffs || 0) - (found.amount || 0));
      // 退款使用记录上的 spent（若不存在则回退为0）
      const refund = Math.max(0, Number(found.spent || 0));
      day.totals.spent = Math.max(0, Math.round(((day.totals.spent || 0) - refund) * 100) / 100);

      // 如果该天无记录，移除该天
      const removedDay = arr.length === 0;
      if (removedDay) days.splice(foundDayIndex, 1);
      await transaction.collection('smoking_records').doc(recDoc._id).update({ records: days });

      // 更新用户总计
      const patch = {};
      if (found.type === 'cigarette') patch.total_cigarettes = Math.max(0, (auth.user.total_cigarettes || 0) - (found.amount || 0));
      else patch.total_puffs = Math.max(0, (auth.user.total_puffs || 0) - (found.amount || 0));
      patch.total_spent = Math.max(0, Math.round(((auth.user.total_spent || 0) - refund) * 100) / 100);
      if (removedDay) patch.total_days = Math.max(0, (auth.user.total_days || 0) - 1);
      await transaction.collection('users').doc(auth.user._id).update(patch);

      await transaction.commit();
      return { code: 200, message: '撤销成功' };
    } catch (e) {
      console.error('undoRecord error', e);
      try { await transaction.rollback(); } catch (_) {}
      return { code: 500, message: '撤销失败' };
    }
  },

  async updateRecord(token, { recordId, amount, note = '' }) {
    const transaction = await this.db.startTransaction();
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) { await transaction.rollback(); return { code: auth.code, message: auth.message }; }
      if (!recordId || !(amount > 0)) { await transaction.rollback(); return { code: 400, message: '参数错误' }; }

      // 读取所有月份并定位记录（事务外预取ID列表，事务内逐doc获取）
      const preAll = await this.records.where({ wx_openid: auth.openId }).get();
      const ids = (preAll.data || []).map(d => d._id);
      const docs = [];
      for (const id of ids) {
        const dr = await transaction.collection('smoking_records').doc(id).get();
        const docObj = normalizeGetDoc(dr);
        if (docObj) docs.push(docObj);
      }
      const rRes = { data: docs };
      if (!rRes.data || rRes.data.length === 0) { await transaction.rollback(); return { code: 404, message: '记录不存在' }; }
      let recDoc = null;
      let days = [];
      let foundDayIndex = -1, foundIndex = -1, found, day;
      for (const d of rRes.data) {
        const rds = d.records || [];
        for (let i = 0; i < rds.length; i++) {
          const arr = rds[i].items || rds[i].records || [];
          const idx = arr.findIndex(r => r.id === recordId);
          if (idx !== -1) { recDoc = d; days = rds; foundDayIndex = i; foundIndex = idx; day = rds[i]; found = arr[idx]; break; }
        }
        if (recDoc) break;
      }
      if (!recDoc || !found) { await transaction.rollback(); return { code: 404, message: '记录不存在' }; }

      // 先不计算delta，待限制目标值后再判断无需更新

      // 回读盒子
      const boxRes = await transaction.collection('tobacco_boxes').doc(found.box_id).get();
      const box = normalizeGetDoc(boxRes);
      if (!box) { await transaction.rollback(); return { code: 404, message: '关联烟盒不存在' }; }
      if (box.wx_openid !== auth.openId) { await transaction.rollback(); return { code: 403, message: '无权操作' }; }

      // 卷烟按盒计费：更新记录不产生费用变化；电子烟更新也不产生费用
      let deltaSpent = 0;
      if (found.type === 'cigarette') {
        // 不按根计费
        deltaSpent = 0;
      } else {
        // electronic: 更新记录不产生费用变化
        deltaSpent = 0;
      }

      // 若为卷烟，限制总消耗不超过预设
      let targetAmount = amount;
      const newSpecs = JSON.parse(JSON.stringify(box.specs || {}));
      if (box.type === 'cigarette') {
        newSpecs.cigarette = newSpecs.cigarette || {};
        const total = Number(newSpecs.cigarette.total || box.specs?.cigarette?.total || 20);
        const consumedNow = Number(newSpecs.cigarette.consumed || 0);
        const withoutThis = Math.max(0, consumedNow - (found.amount || 0));
        const maxAllowed = Math.max(0, Math.min(total, withoutThis + (total - withoutThis))); // equals total
        const remain = Math.max(0, total - withoutThis);
        targetAmount = Math.max(1, Math.min(amount, remain + (found.amount || 0))); // clamp to not exceed total
      }

      const delta = targetAmount - (found.amount || 0);
      if (delta === 0 && (note || '') === (found.note || '')) {
        await transaction.rollback();
        return { code: 200, data: { amount: found.amount }, message: '无需更新' };
      }

      // 更新盒子规格（按delta调整）
      if (box.type === 'cigarette') {
        newSpecs.cigarette = newSpecs.cigarette || {};
        newSpecs.cigarette.consumed = Math.max(0, (newSpecs.cigarette.consumed || 0) + delta);
      } else {
        // electronic: 不进位，仅按delta调整当前口数
        newSpecs.electronic = newSpecs.electronic || {};
        let currentPuffs = newSpecs.electronic.currentCartridgePuffs || 0;
        currentPuffs += delta;
        if (currentPuffs < 0) currentPuffs = 0;
        newSpecs.electronic.currentCartridgePuffs = Math.max(0, currentPuffs);
        newSpecs.electronic.currentCartridge = Math.max(1, newSpecs.electronic.currentCartridge || 1);
      }
      await transaction.collection('tobacco_boxes').doc(found.box_id).update({ specs: newSpecs, update_time: new Date() });

      // 更新日 totals 与记录本身
      found.amount = targetAmount;
      found.note = note || '';
      // 如果记录含有 spent 字段，先用其调整，再叠加 deltaSpent；否则初始化为 0 后叠加
      if (typeof found.spent !== 'number') found.spent = 0;
      found.spent = Math.max(0, Math.round((found.spent + deltaSpent) * 100) / 100);
      if (found.type === 'cigarette') day.totals.cigarettes = Math.max(0, (day.totals.cigarettes || 0) + delta);
      else day.totals.puffs = Math.max(0, (day.totals.puffs || 0) + delta);
      day.totals.spent = Math.max(0, Math.round(((day.totals.spent || 0) + deltaSpent) * 100) / 100);
      await transaction.collection('smoking_records').doc(recDoc._id).update({ records: days });

      // 更新用户总计
      const patch = {};
      if (found.type === 'cigarette') patch.total_cigarettes = Math.max(0, (auth.user.total_cigarettes || 0) + delta);
      else patch.total_puffs = Math.max(0, (auth.user.total_puffs || 0) + delta);
      patch.total_spent = Math.max(0, Math.round(((auth.user.total_spent || 0) + deltaSpent) * 100) / 100);
      await transaction.collection('users').doc(auth.user._id).update(patch);

      await transaction.commit();
      return { code: 200, data: { amount: targetAmount }, message: '更新成功' };
    } catch (e) {
      console.error('updateRecord error', e);
      try { await transaction.rollback(); } catch (_) {}
      return { code: 500, message: '更新失败' };
    }
  },

  // 手动完成当前电子烟烟弹：仅最后一颗时整盒计费，否则不计费；并推进到下一颗，下一颗从0开始
  async completeCartridge(token, { boxId }) {
    // 事务外预取当月文档ID，避免事务内 find
    const authPre = await getOpenIdAndUser(token, this.users);
    if (!authPre.ok) return { code: authPre.code, message: authPre.message };
    const ymPre = formatYearMonth(Date.now());
    let preRecDocId = null;
    try {
      const pre = await this.records.where({ wx_openid: authPre.openId, yearMonth: ymPre }).get();
      if (pre && pre.data && pre.data.length > 0) preRecDocId = pre.data[0]._id;
    } catch (_) {}
    const transaction = await this.db.startTransaction();
    try {
      const auth = authPre; // 事务内复用鉴权结果
      if (!auth.ok) { await transaction.rollback(); return { code: auth.code, message: auth.message }; }
      if (!boxId) { await transaction.rollback(); return { code: 400, message: 'boxId必填' }; }

      // 读取盒子
      const boxRes = await transaction.collection('tobacco_boxes').doc(boxId).get();
      const boxData = Array.isArray(boxRes.data) ? (boxRes.data[0] || null) : (boxRes.data || null);
      if (!boxData) { await transaction.rollback(); return { code: 404, message: '烟盒不存在' }; }
      const box = boxData;
      if (box.wx_openid !== auth.openId) { await transaction.rollback(); return { code: 403, message: '无权操作' }; }
      if (box.is_completed) { await transaction.rollback(); return { code: 400, message: '该烟盒已完成' }; }
      if (box.type !== 'electronic') { await transaction.rollback(); return { code: 400, message: '仅电子烟支持完成烟弹' }; }

      const now = new Date();
      const e = box.specs?.electronic || {};
      const totalCartridges = Number(e.totalCartridges) || 0;
      const currentCartridge = Number(e.currentCartridge || 1);
      const isLastCartridge = (totalCartridges > 0) && (currentCartridge >= totalCartridges);
      const spentForThisCompletion = isLastCartridge ? (Number(box.price) || 0) : 0;

      // 写入完成记录（amount=0，仅计费）到当月文档
      const date = formatDateYYYYMMDD(now);
      const ym = formatYearMonth(now);
      const recId = `${Date.now()}_${Math.random().toString(16).slice(2, 8)}`;
      const recItem = { id: recId, box_id: boxId, type: 'electronic', amount: 0, note: '', timestamp: now.getTime(), spent: Math.round(spentForThisCompletion * 100) / 100, meta: { type: 'electronic', complete: true } };
      let days = [];
      let recDocId = preRecDocId || null;
      if (!recDocId) {
        const addRes = await transaction.collection('smoking_records').add({ wx_openid: auth.openId, yearMonth: ym, records: [] });
        recDocId = addRes.id;
      } else {
        const docRes = await transaction.collection('smoking_records').doc(recDocId).get();
        const docObj = Array.isArray(docRes.data) ? (docRes.data[0] || {}) : (docRes.data || {});
        days = docObj.records || [];
      }

      let day = days.find(d => d.date === date);
      let isNewDay = false;
      if (!day) {
        day = { date, totals: { cigarettes: 0, puffs: 0, spent: 0 }, items: [] };
        days.push(day);
        isNewDay = true;
      }
      (day.items || (day.items = [])).unshift(recItem);
      day.totals.spent = Math.round(((day.totals.spent || 0) + (recItem.spent || 0)) * 100) / 100;

      await transaction.collection('smoking_records').doc(recDocId).update({ records: days });

      // 推进烟弹并清零当前口数
      const newSpecs = JSON.parse(JSON.stringify(box.specs || {}));
      newSpecs.electronic = newSpecs.electronic || {};
      let nextCartridge = (newSpecs.electronic.currentCartridge || 1) + 1;
      newSpecs.electronic.currentCartridgePuffs = 0;

      let boxCompleted = false;
      if ((newSpecs.electronic.totalCartridges || 0) > 0 && nextCartridge > (newSpecs.electronic.totalCartridges || 0)) {
        // 完成最后一颗后盒子完成
        await transaction.collection('tobacco_boxes').doc(boxId).update({ is_completed: true, update_time: now });
        boxCompleted = true;
      } else {
        newSpecs.electronic.currentCartridge = nextCartridge;
        await transaction.collection('tobacco_boxes').doc(boxId).update({ specs: newSpecs, last_used_time: now, update_time: now });
      }

      // 更新用户累计费用（不影响口数）
      const userPatch = { total_spent: Math.round(((auth.user.total_spent || 0) + (recItem.spent || 0)) * 100) / 100 };
      if (isNewDay) userPatch.total_days = (auth.user.total_days || 0) + 1;
      await transaction.collection('users').doc(auth.user._id).update(userPatch);

      await transaction.commit();
      return { code: 200, data: { recordId: recId, timestamp: recItem.timestamp, date, spent: recItem.spent, boxCompleted } };
    } catch (e) {
      console.error('completeCartridge error', e);
      try { await transaction.rollback(); } catch (_) {}
      return { code: 500, message: '完成当前烟弹失败' };
    }
  },

  // ============ 云吸虚拟记录 ============
  async addVirtualRecord(token, { type, amount, note = '' }) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      if (!type || (type !== 'cigarette' && type !== 'electronic')) return { code: 400, message: 'type无效' };
      if (!(amount > 0)) return { code: 400, message: '数量必须大于0' };

      const now = new Date();
      const ym = formatYearMonth(now.getTime());
      const date = formatDateYYYYMMDD(now);

      // 确保用户的虚拟品牌存在（各自类型各一条）
      const vName = type === 'cigarette' ? '轻清云卷烟' : '轻清云电子烟';
      const vSpecs = type === 'cigarette'
        ? { cigarette: { count: 20 } }
        : { electronic: { cartridges: 3, puffsPerCartridge: 300 } };
      let brandId = '';
      try {
        const exist = await this.brands.where({ wx_openid: auth.openId, name: vName, type, is_virtual: true }).get();
        if (exist && exist.data && exist.data.length > 0) {
          brandId = exist.data[0]._id;
        } else {
          const addRes = await this.brands.add({
            name: vName,
            type,
            default_price: 0,
            image: '',
            is_custom: false,
            is_virtual: true,
            wx_openid: auth.openId,
            default_specs: vSpecs
          });
          brandId = addRes.id;
        }
      } catch (_) {
        // 如果品牌处理失败则中止
        return { code: 500, message: '虚拟品牌初始化失败' };
      }

      // 读取或创建当月文档
      const pre = await this.records.where({ wx_openid: auth.openId, yearMonth: ym }).get();
      let recDocId = null;
      let days = [];
      if (!pre || !pre.data || pre.data.length === 0) {
        const addRes = await this.records.add({ wx_openid: auth.openId, yearMonth: ym, records: [] });
        recDocId = addRes.id;
      } else {
        recDocId = pre.data[0]._id;
        const docRes = await this.records.doc(recDocId).get();
        const docObj = normalizeGetDoc(docRes) || {};
        days = docObj.records || [];
      }

      const recId = `${Date.now()}_${Math.random().toString(16).slice(2, 8)}`;
      const recItem = {
        id: recId,
        box_id: '__VIRTUAL__',
        type,
        amount,
        note,
        timestamp: now.getTime(),
        spent: 0,
        fake: true,
        meta: {
          virtual: true,
          brandId,
          brandName: vName,
          brandType: type,
          specs: vSpecs
        }
      };

      let day = days.find(d => d.date === date);
      if (!day) {
        day = { date, totals: { cigarettes: 0, puffs: 0, spent: 0 }, items: [] };
        days.push(day);
      }
      (day.items || (day.items = [])).unshift(recItem);
      if (type === 'cigarette') day.totals.cigarettes += amount; else day.totals.puffs += amount;
      // spent 恒为0

      await this.records.doc(recDocId).update({ records: days });
      return { code: 200, data: { recordId: recId, timestamp: recItem.timestamp, amount } };
    } catch (e) {
      console.error('addVirtualRecord error', e);
      return { code: 500, message: '新增云吸记录失败' };
    }
  },

  async listRecords(token, { startDate, endDate } = {}) {
    try {
      const auth = await getOpenIdAndUser(token, this.users);
      if (!auth.ok) return { code: auth.code, message: auth.message };
      // 按月份范围读取（每用户每月一文档）
      let months = getYearMonthRange(startDate, endDate);
      if (!months) {
        // 未提供区间则默认只读当月
        months = [formatYearMonth(Date.now())];
      }
      const res = await this.records.where({ wx_openid: auth.openId, yearMonth: this.cmd.in(months) }).get();
      const docs = Array.isArray(res.data) ? res.data : [];
      if (docs.length === 0) return { code: 200, data: [] };

      const s = startDate ? startDate.replace(/-/g, '/') : null;
      const e = endDate ? endDate.replace(/-/g, '/') : null;
      const flat = [];

      for (const doc of docs) {
        const days = Array.isArray(doc.records) ? doc.records : [];
        for (const d of days) {
          // 日期范围过滤（基于字符串比较仍保持合理性：YYYY/MM/DD）
          const dayStr = d && d.date ? d.date.replace(/-/g, '/') : '';
          if (s && dayStr < s) continue;
          if (e && dayStr > e) continue;
          for (const r of ((d.items || d.records || []))) {
            flat.push({ id: r.id, boxId: r.box_id, type: r.type, amount: r.amount, note: r.note || '', timestamp: r.timestamp, spent: typeof r.spent === 'number' ? r.spent : 0, fake: !!r.fake, meta: r.meta || null });
          }
        }
      }

      // 按时间倒序并返回
      flat.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
      return { code: 200, data: flat };
    } catch (e2) {
      console.error('listRecords error', e2);
      return { code: 500, message: '获取记录失败' };
    }
  }
};


