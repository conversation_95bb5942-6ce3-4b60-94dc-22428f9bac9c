'use strict'

const { verifyToken } = require('./jwt-utils')

async function getAuth(token, usersCollection) {
  const v = verifyToken(token)
  if (!v.valid) return { ok: false, code: 401, message: 'Token无效，请重新登录' }
  const openId = v.payload.openId
  const u = await usersCollection.where({ wx_openid: openId }).get()
  if (!u || !u.data || u.data.length === 0) return { ok: false, code: 404, message: '用户不存在' }
  return { ok: true, openId, user: u.data[0] }
}

function nowMs() { return Date.now() }

module.exports = {
  _before() {
    this.db = uniCloud.database()
    this.users = this.db.collection('users')
    this.col = this.db.collection('achievements')
  },

  /** 获取用户已解锁成就列表 */
  async getUserAchievements(token) {
    try {
      const auth = await getAuth(token, this.users)
      if (!auth.ok) return { code: auth.code, message: auth.message }
      const res = await this.col.where({ wx_openid: auth.openId }).get()
      if (!res || !res.data || res.data.length === 0) {
        return { code: 200, data: { achievements: [], unread: 0 } }
      }
      const doc = res.data[0]
      const list = Array.isArray(doc.achievements) ? doc.achievements : []
      const unread = list.reduce((n, a) => n + (a.read ? 0 : 1), 0)
      return { code: 200, data: { achievements: list, unread } }
    } catch (e) {
      console.error('getUserAchievements error', e)
      return { code: 500, message: '获取成就失败' }
    }
  },

  /** 同步解锁：幂等去重合并 */
  async syncUnlocked(token, { unlocked = [] } = {}) {
    try {
      const auth = await getAuth(token, this.users)
      if (!auth.ok) return { code: auth.code, message: auth.message }
      const res = await this.col.where({ wx_openid: auth.openId }).get()
      const base = (res && res.data && res.data[0]) || null
      const existed = new Map()
      const arr = []
      if (base && Array.isArray(base.achievements)) {
        for (const a of base.achievements) { if (a && a.id) { existed.set(a.id, a); arr.push(a) } }
      }
      const newlyAddedIds = []
      for (const u of unlocked) {
        if (!u || !u.id) continue
        if (!existed.has(u.id)) {
          const obj = { id: u.id, unlockedAt: Number(u.unlockedAt || nowMs()), read: false }
          arr.push(obj)
          existed.set(u.id, obj)
          newlyAddedIds.push(u.id)
        }
      }
      if (!base) {
        await this.col.add({ wx_openid: auth.openId, achievements: arr, updatedAt: nowMs() })
      } else if (newlyAddedIds.length > 0) {
        await this.col.doc(base._id).update({ achievements: arr, updatedAt: nowMs() })
      }
      const unread = arr.reduce((n, a) => n + (a.read ? 0 : 1), 0)
      return { code: 200, data: { newlyAddedIds, achievements: arr, unread } }
    } catch (e) {
      console.error('syncUnlocked error', e)
      return { code: 500, message: '同步失败' }
    }
  },

  /** 标记若干成就为已读 */
  async markRead(token, { ids = [] } = {}) {
    try {
      const auth = await getAuth(token, this.users)
      if (!auth.ok) return { code: auth.code, message: auth.message }
      const res = await this.col.where({ wx_openid: auth.openId }).get()
      if (!res || !res.data || res.data.length === 0) return { code: 200, data: { achievements: [], unread: 0 } }
      const doc = res.data[0]
      const setIds = new Set(Array.isArray(ids) ? ids : [])
      const list = (doc.achievements || []).map(a => {
        if (a && a.id && (setIds.size === 0 || setIds.has(a.id))) {
          return { ...a, read: true, readAt: nowMs() }
        }
        return a
      })
      await this.col.doc(doc._id).update({ achievements: list, updatedAt: nowMs() })
      const unread = list.reduce((n, a) => n + (a.read ? 0 : 1), 0)
      return { code: 200, data: { achievements: list, unread } }
    } catch (e) {
      console.error('markRead error', e)
      return { code: 500, message: '标记已读失败' }
    }
  },

  /** 获取未读数量 */
  async getUnreadCount(token) {
    try {
      const auth = await getAuth(token, this.users)
      if (!auth.ok) return { code: auth.code, message: auth.message }
      const res = await this.col.where({ wx_openid: auth.openId }).get()
      if (!res || !res.data || res.data.length === 0) return { code: 200, data: { unread: 0 } }
      const doc = res.data[0]
      const unread = (doc.achievements || []).reduce((n, a) => n + (a.read ? 0 : 1), 0)
      return { code: 200, data: { unread } }
    } catch (e) {
      console.error('getUnreadCount error', e)
      return { code: 500, message: '获取未读数失败' }
    }
  }
}


