'use strict'

const jwt = require('jsonwebtoken')

// 注意：生产环境建议改为使用uni-config-center或环境变量
const JWT_SECRET = 'cigarette-book-jwt-secret'
const TOKEN_EXPIRES_IN = '7d'

function generateToken(openId) {
  const payload = { openId }
  const options = { expiresIn: TOKEN_EXPIRES_IN, issuer: 'cigarette-book', subject: openId }
  return jwt.sign(payload, JWT_SECRET, options)
}

function verifyToken(token) {
  try {
    if (!token) return { valid: false, error: 'Token为空' }
    const options = { issuer: 'cigarette-book' }
    const payload = jwt.verify(token, JWT_SECRET, options)
    return { valid: true, payload }
  } catch (error) {
    let errorMessage = 'Token验证失败'
    if (error.name === 'TokenExpiredError') errorMessage = 'Token已过期'
    else if (error.name === 'JsonWebTokenError') errorMessage = 'Token格式错误'
    else if (error.name === 'NotBeforeError') errorMessage = 'Token未生效'
    return { valid: false, error: errorMessage }
  }
}

module.exports = { generateToken, verifyToken }


