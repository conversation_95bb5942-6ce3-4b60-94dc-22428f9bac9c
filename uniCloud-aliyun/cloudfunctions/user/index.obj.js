const { generateToken, verifyToken } = require("./jwt-utils");

const expiresIn = 7 * 24 * 60 * 60; // 7天（秒）

// 私有方法：通过微信code获取openId
async function getWxOpenId(code, appId, appSecret) {
  try {
    const wxApiUrl = "https://api.weixin.qq.com/sns/jscode2session";
    const url = `${wxApiUrl}?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;

    const result = await uniCloud.httpclient.request(url, {
      method: "GET",
      dataType: "json",
    });

    if (result.data.errcode) {
      return {
        success: false,
        message: `微信登录失败: ${result.data.errmsg}`,
      };
    }

    return {
      success: true,
      data: {
        openId: result.data.openid,
        sessionKey: result.data.session_key,
        unionId: result.data.unionid,
      },
    };
  } catch (error) {
    console.error("获取微信openId失败:", error);
    return {
      success: false,
      message: "微信服务异常",
    };
  }
}

// 私有方法：验证token并获取用户信息
async function _verifyTokenAndGetUser(token, usersCollection) {
  const verifyResult = verifyToken(token);
  if (!verifyResult.valid) {
    return {
      success: false,
      code: 401,
      message: "Token无效，请重新登录"
    };
  }

  const userRes = await usersCollection
    .where({ wx_openid: verifyResult.payload.openId })
    .get();

  if (userRes.data.length === 0) {
    return {
      success: false,
      code: 404,
      message: "用户不存在"
    };
  }

  return {
    success: true,
    data: {
      openId: verifyResult.payload.openId,
      userInfo: userRes.data[0]
    }
  };
}

module.exports = {
  // 云对象初始化
  _before: function () {
    this.db = uniCloud.database();
    this.usersCollection = this.db.collection("users");
    // 这里需要配置你的微信小程序AppId和AppSecret
    this.appId = "wx1f621968d2a667de";
    this.appSecret = "023494fc97e63a932c1043d71e416c60";
  },

  /**
   * 局部更新用户偏好设置（带嵌套合并）
   * @param {string} token JWT token
   * @param {Object} patch 要更新的 preferences 子集
   * @returns {Object} 更新结果，返回最新 preferences
   */
  async updatePreferences(token, patch) {
    try {
      // 验证token并获取用户信息
      const userResult = await _verifyTokenAndGetUser(token, this.usersCollection);
      if (!userResult.success) {
        return {
          code: userResult.code,
          message: userResult.message,
        };
      }

      const { openId } = userResult.data;

      // 读取当前用户
      const currentUserRes = await this.usersCollection.where({ wx_openid: openId }).get();
      if (currentUserRes.data.length === 0) {
        return { code: 404, message: "用户不存在" };
      }

      const current = currentUserRes.data[0];
      const currentPref = current.preferences || {};

      // 校验 patch 是对象
      if (!patch || typeof patch !== 'object') {
        return { code: 400, message: '参数错误：patch 必须是对象' };
      }

      // 允许的顶层字段
      const allowedTopLevel = new Set([
        'theme',
        'reminder_enabled',
        'daily_limit',
        'max_active_boxes',
        'default_cigarette_count',
        'default_electronic_puffs',
        'haptics_enabled',
        'quick_record_presets',
        'auto_suggest_presets'
      ]);

      // 顶层非法字段检查
      for (const key of Object.keys(patch)) {
        if (!allowedTopLevel.has(key)) {
          return { code: 400, message: `不支持的字段: ${key}` };
        }
      }

      // 生成合并后的 preferences
      const merged = { ...currentPref };

      // 简单字段浅合并
      const shallowKeys = ['theme','reminder_enabled','max_active_boxes','default_cigarette_count','default_electronic_puffs','haptics_enabled','auto_suggest_presets'];
      shallowKeys.forEach(k => {
        if (patch[k] !== undefined) merged[k] = patch[k];
      });

      // daily_limit 嵌套合并
      if (patch.daily_limit !== undefined) {
        const dl = merged.daily_limit || {};
        const p = patch.daily_limit || {};
        merged.daily_limit = {
          enabled: p.enabled !== undefined ? p.enabled : dl.enabled,
          cigarettes: p.cigarettes !== undefined ? p.cigarettes : dl.cigarettes,
          puffs: p.puffs !== undefined ? p.puffs : dl.puffs
        };
      }

      // quick_record_presets 嵌套合并
      if (patch.quick_record_presets !== undefined) {
        const qr = merged.quick_record_presets || {};
        const p = patch.quick_record_presets || {};
        merged.quick_record_presets = {
          cigarette: p.cigarette !== undefined ? p.cigarette : qr.cigarette,
          electronic: p.electronic !== undefined ? p.electronic : qr.electronic
        };
      }

      // 更新数据库
      const updateRes = await this.usersCollection.where({ wx_openid: openId }).update({ preferences: merged });
      if (updateRes.updated === 0) {
        return { code: 500, message: '更新失败' };
      }

      return {
        code: 200,
        message: '偏好设置更新成功',
        data: merged
      };
    } catch (error) {
      console.error('更新偏好设置失败:', error);
      return { code: 500, message: '更新偏好设置服务异常' };
    }
  },

  /**
   * 检查用户是否已存在（不创建账号）
   * @param {string} code 微信登录临时code
   * @returns {Object} { code:200, data:{ exists:boolean } }
   */
  async checkUserExists(code) {
    try {
      if (!code) {
        return { code: 400, message: "登录code不能为空" };
      }
      const wxLoginResult = await getWxOpenId(code, this.appId, this.appSecret);
      if (!wxLoginResult.success) {
        return { code: 400, message: wxLoginResult.message };
      }
      const { openId } = wxLoginResult.data;
      const userRes = await this.usersCollection.where({ wx_openid: openId }).get();
      return { code: 200, message: "OK", data: { exists: userRes.data.length > 0 } };
    } catch (error) {
      console.error("检查用户是否存在失败:", error);
      return { code: 500, message: "服务异常" };
    }
  },

  /**
   * 微信小程序登录
   * @param {string} code 微信登录临时code
   * @returns {Object} 登录结果
   */
  async login(code) {
    try {
      if (!code) {
        return {
          code: 400,
          message: "登录code不能为空",
        };
      }

      // 1. 通过code获取微信用户openId
      const wxLoginResult = await getWxOpenId(code, this.appId, this.appSecret);
      if (!wxLoginResult.success) {
        return {
          code: 400,
          message: wxLoginResult.message,
        };
      }

      const { openId } = wxLoginResult.data;

      // 2. 查询用户是否存在
      const userRes = await this.usersCollection
        .where({ wx_openid: openId })
        .get();

      let userInfo;
      let isNewUser = false;

      if (userRes.data.length > 0) {
        // 老用户，直接获取用户信息
        userInfo = userRes.data[0];

        // 更新最后登录时间
        await this.usersCollection.doc(userInfo._id).update({
          last_login_time: new Date(),
          login_count: this.db.command.inc(1),
        });
      } else {
        // 新用户，创建用户记录
        // 默认昵称：从词池随机 + 2~4位随机数字
        const nicknamePool = ["烟友","小抽","云雾","青烟","吞云","吐雾","烟客","飘烟"];
        const randomWord = nicknamePool[Math.floor(Math.random() * nicknamePool.length)];
        const randomDigitsLength = 2 + Math.floor(Math.random() * 3); // 2~4 位
        const randomDigits = Math.floor(Math.random() * Math.pow(10, randomDigitsLength)).toString().padStart(randomDigitsLength, '0');
        const generatedNickname = `${randomWord}${randomDigits}`;

        const newUser = {
          wx_openid: openId,
          nickname: generatedNickname,
          avatar_url: "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/default-avatar.jpg",
          gender: 0,
          total_cigarettes: 0,
          total_puffs: 0,
          total_spent: 0,
          total_days: 0,
          user_level: 1,
          status: 0,
          register_time: new Date(),
          last_login_time: new Date(),
          login_count: 1,
          preferences: {
            theme: "light",
            reminder_enabled: true,
            daily_limit: {
              enabled: false,
              cigarettes: 10,
              puffs: 100
            },
            max_active_boxes: 3,
            default_cigarette_count: 20,
            default_electronic_puffs: 300,
            haptics_enabled: true,
            quick_record_presets: { cigarette: [1,2,3], electronic: [5,10,20] },
            auto_suggest_presets: true
          }
        };

        const createRes = await this.usersCollection.add(newUser);
        userInfo = {
          _id: createRes.id,
          ...newUser,
        };
        isNewUser = true;
      }

      // 3. 生成JWT token
      const token = generateToken(openId);

      return {
        code: 200,
        message: "登录成功",
        data: {
          token,
          userInfo,
          isNewUser,
          expiresIn: expiresIn,
        },
      };
    } catch (error) {
      console.error("登录失败:", error);
      return {
        code: 500,
        message: "登录服务异常",
      };
    }
  },

  /**
   * 刷新token
   * @param {string} token 当前token
   * @returns {Object} 刷新结果
   */
  async refreshToken(token) {
    try {
      // 验证当前token
      const verifyResult = verifyToken(token);
      if (!verifyResult.valid) {
        return {
          code: 401,
          message: "Token无效，请重新登录",
        };
      }

      // 生成新token
      const newToken = generateToken(verifyResult.payload.openId);

      return {
        code: 200,
        message: "Token刷新成功",
        data: {
          token: newToken,
          expiresIn: expiresIn,
        },
      };
    } catch (error) {
      console.error("Token刷新失败:", error);
      return {
        code: 500,
        message: "Token刷新服务异常",
      };
    }
  },

  /**
   * 获取用户信息
   * @param {string} token JWT token
   * @returns {Object} 用户信息
   */
  async getUserInfo(token) {
    try {
      // 验证token并获取用户信息
      const userResult = await _verifyTokenAndGetUser(token, this.usersCollection);
      if (!userResult.success) {
        return {
          code: userResult.code,
          message: userResult.message,
        };
      }

      const { userInfo } = userResult.data;

      return {
        code: 200,
        message: "获取用户信息成功",
        data: {
          _id: userInfo._id,
          wx_openid: userInfo.wx_openid,
          nickname: userInfo.nickname,
          avatar_fileId: userInfo.avatar_fileId,
          avatar_url: userInfo.avatar_url,
          gender: userInfo.gender,
          birth_date: userInfo.birth_date,
          phone: userInfo.phone,
          email: userInfo.email,
          total_cigarettes: userInfo.total_cigarettes || 0,
          total_puffs: userInfo.total_puffs || 0,
          total_spent: userInfo.total_spent || 0,
          total_days: userInfo.total_days || 0,
          user_level: userInfo.user_level || 1,
          register_time: userInfo.register_time,
          last_login_time: userInfo.last_login_time,
          login_count: userInfo.login_count || 0,
          preferences: userInfo.preferences || {},
        },
      };
    } catch (error) {
      console.error("获取用户信息失败:", error);
      return {
        code: 500,
        message: "获取用户信息服务异常",
      };
    }
  },

  /**
   * 更新用户信息
   * @param {string} token JWT token
   * @param {Object} userInfo 用户信息
   * @returns {Object} 更新结果
   */
  async updateUserInfo(token, userInfo) {
    try {
      // 验证token并获取用户信息
      const userResult = await _verifyTokenAndGetUser(token, this.usersCollection);
      if (!userResult.success) {
        return {
          code: userResult.code,
          message: userResult.message,
        };
      }

      const { openId } = userResult.data;

      // 验证用户信息
      const updateData = {};
      
      if (userInfo.nickname !== undefined) {
        if (!userInfo.nickname || userInfo.nickname.length > 20) {
          return {
            code: 400,
            message: "昵称长度必须在1-20个字符之间",
          };
        }
        updateData.nickname = userInfo.nickname;
      }

      if (userInfo.avatar_fileId !== undefined) {
        updateData.avatar_fileId = userInfo.avatar_fileId;
      }

      if (userInfo.avatar_url !== undefined) {
        updateData.avatar_url = userInfo.avatar_url;
      }

      if (userInfo.gender !== undefined) {
        if (![0, 1, 2].includes(userInfo.gender)) {
          return {
            code: 400,
            message: "性别参数错误",
          };
        }
        updateData.gender = userInfo.gender;
      }

      if (userInfo.birth_date !== undefined) {
        updateData.birth_date = userInfo.birth_date;
      }

      if (userInfo.phone !== undefined) {
        updateData.phone = userInfo.phone;
      }

      if (userInfo.email !== undefined) {
        updateData.email = userInfo.email;
      }

      if (userInfo.preferences !== undefined) {
        updateData.preferences = userInfo.preferences;
      }

      if (Object.keys(updateData).length === 0) {
        return {
          code: 400,
          message: "没有需要更新的信息",
        };
      }

      // 更新用户信息
      const updateRes = await this.usersCollection
        .where({ wx_openid: openId })
        .update(updateData);

      if (updateRes.updated === 0) {
        return {
          code: 404,
          message: "用户不存在",
        };
      }

      // 获取更新后的完整用户信息
      const updatedUserRes = await this.usersCollection
        .where({ wx_openid: openId })
        .get();

      if (updatedUserRes.data.length === 0) {
        return {
          code: 404,
          message: "获取更新后用户信息失败",
        };
      }

      return {
        code: 200,
        message: "用户信息更新成功",
        data: updatedUserRes.data[0],
      };
    } catch (error) {
      console.error("更新用户信息失败:", error);
      return {
        code: 500,
        message: "更新用户信息服务异常",
      };
    }
  },

  /**
   * 更新用户统计数据
   * @param {string} token JWT token
   * @param {Object} stats 统计数据
   * @returns {Object} 更新结果
   */
  async updateUserStats(token, stats) {
    try {
      // 验证token并获取用户信息
      const userResult = await _verifyTokenAndGetUser(token, this.usersCollection);
      if (!userResult.success) {
        return {
          code: userResult.code,
          message: userResult.message,
        };
      }

      const { openId } = userResult.data;

      // 验证统计数据
      const updateData = {};
      
      if (stats.total_cigarettes !== undefined && stats.total_cigarettes >= 0) {
        updateData.total_cigarettes = stats.total_cigarettes;
      }

      if (stats.total_puffs !== undefined && stats.total_puffs >= 0) {
        updateData.total_puffs = stats.total_puffs;
      }

      if (stats.total_spent !== undefined && stats.total_spent >= 0) {
        updateData.total_spent = stats.total_spent;
      }

      if (stats.total_days !== undefined && stats.total_days >= 0) {
        updateData.total_days = stats.total_days;
      }

      if (Object.keys(updateData).length === 0) {
        return {
          code: 400,
          message: "没有有效的统计数据需要更新",
        };
      }

      // 更新用户统计
      const updateRes = await this.usersCollection
        .where({ wx_openid: openId })
        .update(updateData);

      if (updateRes.updated === 0) {
        return {
          code: 404,
          message: "用户不存在",
        };
      }

      return {
        code: 200,
        message: "用户统计数据更新成功",
        data: updateData,
      };
    } catch (error) {
      console.error("更新用户统计数据失败:", error);
      return {
        code: 500,
        message: "更新用户统计数据服务异常",
      };
    }
  },
};
