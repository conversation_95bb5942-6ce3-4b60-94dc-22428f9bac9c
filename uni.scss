/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */


//  引入iconfont
@font-face {
  font-family: "iconfont";
  src: url("https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/iconfont/iconfont.woff2?expire_at=1749021971&er_sign=ebdb5edd33c30da584374db4868c918f")
      format("woff2"),
    url("https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/iconfont/iconfont.woff?expire_at=1749021971&er_sign=c40fdcd8561fc6a364777a035d54904b")
      format("woff"),
    url("https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/iconfont/iconfont.ttf?expire_at=1749021971&er_sign=914cac32a7976884f541ad73183f7519")
      format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color:#333;//基本色
$uni-text-color-inverse:#fff;//反色
$uni-text-color-grey:#999;//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable:#c0c0c0;

/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:24rpx;
$uni-font-size-base:28rpx;
$uni-font-size-lg:32rpx;

/* 图片尺寸 */
$uni-img-size-sm:40rpx;
$uni-img-size-base:52rpx;
$uni-img-size-lg:80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10rpx;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:40rpx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:52rpx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:30rpx;

/* 轻清戒烟日记应用专用样式变量 */

/* 项目专用颜色扩展 */
$app-primary-color: #2C5282;          // 深蓝主色
$app-primary-light: #4A90E2;          // 浅蓝色
$app-primary-dark: #1A365D;           // 深蓝色
$app-secondary-color: #F6AD55;        // 温暖橙色
$app-accent-color: #48BB78;           // 成功绿色
$app-warning-color: #ED8936;          // 警告橙色
$app-danger-color: #E53E3E;           // 危险红色

/* 功能性颜色 */
$app-card-bg: #FFFFFF;                // 卡片背景
$app-section-bg: #F7FAFC;             // 区域背景
$app-border-light: #E2E8F0;           // 浅边框
$app-border-dark: #CBD5E0;            // 深边框
$app-text-primary: #2D3748;           // 主要文字
$app-text-secondary: #718096;         // 次要文字
$app-text-muted: #A0AEC0;             // 弱化文字
$app-text-white: #FFFFFF;             // 白色文字

/* 尺寸规范 */
$app-card-radius: 24rpx;               // 卡片圆角
$app-button-radius: 16rpx;             // 按钮圆角
$app-input-radius: 12rpx;              // 输入框圆角
$app-spacing-xs: 8rpx;                 // 最小间距
$app-spacing-sm: 16rpx;                // 小间距
$app-spacing-md: 32rpx;                // 中间距
$app-spacing-lg: 48rpx;                // 大间距
$app-spacing-xl: 64rpx;                // 超大间距

/* 字体尺寸 */
$app-font-size-xs: 20rpx;              // 最小字体
$app-font-size-sm: 24rpx;              // 小字体
$app-font-size-base: 28rpx;            // 基础字体
$app-font-size-lg: 32rpx;              // 大字体
$app-font-size-xl: 36rpx;              // 超大字体
$app-font-size-xxl: 40rpx;             // 标题字体

/* 阴影效果 */
$app-shadow-sm: 0 2rpx 6rpx rgba(0,0,0,0.1);
$app-shadow-md: 0 8rpx 12rpx rgba(0,0,0,0.1);
$app-shadow-lg: 0 20rpx 30rpx rgba(0,0,0,0.1);

/* 动画时长 */
$app-transition-fast: 0.15s;          // 快速动画
$app-transition-base: 0.25s;          // 基础动画
$app-transition-slow: 0.35s;          // 慢速动画

/* 主题 CSS 变量（运行时切换） */
:root,
.theme-light {
  --color-bg-page: #F7FAFC;
  --color-bg-card: #FFFFFF;
  --color-bg-section: #F7FAFC;
  --color-border: #E2E8F0;
  --color-disabled-bg: #E2E8F0;
  --color-disabled-text: #A0AEC0;
  --color-disabled-border: #E2E8F0;
  --color-text-primary: #2D3748;
  --color-text-secondary: #718096;
  --color-text-muted: #A0AEC0;
  --color-text-white: #FFFFFF;
  --color-primary: #2C5282;
  --color-primary-press: #1A365D;
  --color-secondary: #F6AD55;
  --color-accent: #48BB78;
  --color-danger: #E53E3E;
  --color-overlay: rgba(17, 24, 39, 0.60);
  --shadow-sm: 0 2rpx 6rpx rgba(0,0,0,0.1);
  --shadow-md: 0 8rpx 12rpx rgba(0,0,0,0.1);
  --shadow-lg: 0 20rpx 30rpx rgba(0,0,0,0.1);
}

.theme-dark {
  --color-bg-page: #111827;
  --color-bg-card: #1F2937;
  --color-bg-section: #111827;
  --color-border: #374151;
  --color-disabled-bg: #4B5563;
  --color-disabled-text: #D1D5DB;
  --color-disabled-border: #4B5563;
  --color-cancel-bg: transparent;
  --color-cancel-text: #E5E7EB;
  --color-cancel-border: #4B5563;
  --color-text-primary: #E5E7EB;
  --color-text-secondary: #9CA3AF;
  --color-text-muted: #6B7280;
  --color-text-white: #FFFFFF;
  --color-primary: #60A5FA; /* 深色主题主色稍亮 */
  --color-primary-press: #3B82F6;
  --color-secondary: #F59E0B;
  --color-accent: #34D399;
  --color-danger: #F87171;
  --color-overlay: rgba(255, 255, 255, 0.14);
  --shadow-sm: 0 2rpx 6rpx rgba(0,0,0,0.6);
  --shadow-md: 0 8rpx 12rpx rgba(0,0,0,0.6);
  --shadow-lg: 0 20rpx 30rpx rgba(0,0,0,0.6);
}

/* 通用按钮体系 */
.btn {
  border: none;
  border-radius: $app-button-radius;
  padding: $app-spacing-sm $app-spacing-md;
  font-size: $app-font-size-base;
  transition: all $app-transition-base ease;
}
.btn--primary {
  background-color: var(--color-primary);
  color: var(--color-text-white);
}
.btn--primary:active {
  background-color: var(--color-primary-press);
}
.btn--secondary {
  background-color: var(--color-secondary);
  color: var(--color-text-white);
}
.btn--danger {
  background-color: var(--color-danger);
  color: var(--color-text-white);
}
.btn.is-disabled {
  opacity: 0.6;
}
.btn--sm { padding: $app-spacing-xs $app-spacing-sm; font-size: $app-font-size-sm; }
.btn--lg { padding: $app-spacing-md $app-spacing-lg; font-size: $app-font-size-lg; }

/* 发丝线工具类 */
.hairline {
  position: relative;
}
.hairline::after {
  content: '';
  position: absolute;
  left: 0; right: 0; bottom: 0;
  border-bottom: 1px solid var(--color-border);
  transform: scaleY(0.5);
  transform-origin: 0 100%;
}
