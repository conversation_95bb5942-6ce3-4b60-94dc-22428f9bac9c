import App from "./App";
import "./utils/dayjs-setup.js";

// #ifndef VUE3
import Vue from "vue";
import "./uni.promisify.adaptor";
Vue.config.productionTip = false;
App.mpType = "app";

Vue.mixin({
  onShareAppMessage() {
    return {
      title: "轻清戒烟日记",
      path: `/pages/index/index`,
    };
  },
  onShareTimeline() {
    return {
      title: "轻清戒烟日记",
    };
  },
});

const app = new Vue({
  ...App,
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";
export function createApp() {
  const app = createSSRApp(App);
  app.mixin({  
    onShareAppMessage() {
      return {
        title: "轻清戒烟日记",
        path: `/pages/index/index`,
      };
    },
    onShareTimeline() {
      return {
        title: "轻清戒烟日记",
      };
    },
  }) 
  return {
    app,
  };
}
// #endif
