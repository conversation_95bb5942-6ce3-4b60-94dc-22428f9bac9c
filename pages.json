{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "轻清戒烟日记",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/profile/profile",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/add-box/add-box",
			"style": {
				"navigationBarTitleText": "添加烟盒",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/record-consumption/record",
			"style": {
				"navigationBarTitleText": "消费记录",
				"navigationStyle": "default"
			}
		},
    {
      "path": "pages/virtual-smoking/virtual",
      "style": {
        "navigationBarTitleText": "云吸训练",
        "navigationStyle": "default"
      }
    },
		{
			"path": "pages/tobacco-collection/collection",
			"style": {
				"navigationBarTitleText": "我抽过的烟",
				"navigationStyle": "default"
			}
		},

		{
			"path": "pages/charts/charts",
			"style": {
				"navigationBarTitleText": "图表",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/statistics/statistics",
			"style": {
				"navigationBarTitleText": "数据统计",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/settings/settings",
			"style": {
				"navigationBarTitleText": "设置",
				"navigationStyle": "default"
			}
		}
		,
		{
			"path": "pages/help/help",
			"style": {
				"navigationBarTitleText": "帮助中心",
				"navigationStyle": "default"
			}
		}
		,
		{
			"path": "pages/achievements/achievements",
			"style": {
				"navigationBarTitleText": "解锁成就",
				"navigationStyle": "default"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "轻清戒烟日记",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#F7FAFC"
	},
	"tabBar": {
		"color": "#718096",
		"selectedColor": "#2C5282",
		"backgroundColor": "#FFFFFF",
		"borderStyle": "black",
		"list": [
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/icons/home-deactive.png",
				"selectedIconPath": "static/icons/home-active.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/charts/charts",
				"iconPath": "static/icons/charts-deactive.png",
				"selectedIconPath": "static/icons/charts-active.png",
				"text": "图表"
			},
			{
				"pagePath": "pages/profile/profile",
				"iconPath": "static/icons/mine-deactive.png",
				"selectedIconPath": "static/icons/mine-active.png",
				"text": "我的"
			}
		]
	},
	"uniIdRouter": {}
}
