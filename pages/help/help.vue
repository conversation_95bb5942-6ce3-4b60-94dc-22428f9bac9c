<template>
  <view class="help-center" :class="currentTheme">
    <!-- 主体：左侧导航 + 右侧内容 -->
    <view class="help-content card">
      <!-- 左侧导航 -->
      <view class="sidebar">
        <view
          v-for="(item, index) in helpTopics"
          :key="index"
          class="sidebar-item"
          :class="{ active: currentTopicIndex === index }"
          @click="setCurrentTopic(index)"
        >
          <text class="sidebar-item-text">{{ item.title }}</text>
        </view>
      </view>

      <!-- 右侧内容区域 -->
      <view class="content-area">
        <scroll-view
          scroll-y="true"
          class="content-scroll"
          :style="{ height: '100%' }"
        >
          <view class="topic-content" v-if="currentTopic">
            <!-- 提示框（可选） -->
            <view class="alert-box" v-if="currentTopic.alert">
              <view class="alert-icon"
                ><text class="iconfont">&#xe884;</text></view
              >
              <view class="alert-text">{{ currentTopic.alert }}</view>
            </view>

            <!-- QA 列表 -->
            <view
              class="qa-list"
              v-if="currentTopic.qaList && currentTopic.qaList.length"
            >
              <view
                class="qa-item"
                v-for="(qa, qaIndex) in currentTopic.qaList"
                :key="qaIndex"
              >
                <view class="question">
                  <text class="question-icon">Q</text>
                  <text class="question-text">{{ qa.question }}</text>
                </view>
                <view class="answer">
                  <text class="answer-icon">A</text>
                  <text class="answer-text">{{ qa.answer }}</text>
                </view>
                <view class="qa-images" v-if="qa.images && qa.images.length">
                  <view
                    class="qa-image-section"
                    v-for="(img, imgIndex) in qa.images"
                    :key="imgIndex"
                  >
                    <view class="image-container">
                      <image
                        :src="img"
                        mode="aspectFit"
                        class="help-image"
                        @error="
                          onQaImageError(currentTopicIndex, qaIndex, imgIndex)
                        "
                      />
                    </view>
                  </view>
                </view>
                <view class="qa-image-section" v-else-if="qa.image">
                  <view class="image-container">
                    <image
                      :src="qa.image"
                      mode="aspectFit"
                      class="help-image"
                      @error="onQaImageError(currentTopicIndex, qaIndex)"
                    />
                  </view>
                </view>
              </view>
            </view>

            <!-- 兼容的全局图片（保留） -->
            <view class="image-section" v-if="currentTopic.image">
              <view class="image-container">
                <image
                  :src="currentTopic.image"
                  class="help-image"
                  mode="aspectFit"
                  @error="onTopicImageError(currentTopicIndex)"
                />
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 底部客服按钮 -->
    <view class="customer-service-section">
      <view class="service-hint">还有问题？我们来帮你解决</view>
      <button open-type="contact" class="customer-service-button">
        <text class="iconfont service-icon">&#xe884;</text>
        <text class="service-text">联系在线客服</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";
import { useTheme } from "../../composables/useTheme.js";

const { currentTheme } = useTheme();

// 左侧导航当前主题索引
const currentTopicIndex = ref(0);

// 帮助主题数据（每条支持 images 多图；兼容 image 单图，占位留空）
const helpTopics = ref([
  {
    title: "登录与资料",
    alert: "",
    qaList: [
      {
        question: "如何登录？",
        answer: "登录是默认进行的，当你进入当前小程序，就会静默登录",
        images: [],
      },
      {
        question: "头像与昵称",
        answer:
          "静默登录后，系统会为你随机生成一个昵称，你也可以通过点击编辑，在弹出的弹窗中修改你喜欢的昵称和头像",
        images: [
          "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/login1.png",
          "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/login2.png",
        ],
      },
      {
        question: "如何退出？",
        answer:
          "点击设置工具栏，即可进入设置页面，滑动到底部，点击退出登录，即可退出当前账号",
        images: [
          "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/login3.png",
          "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/login4.png",
          "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/login5.png",
        ],
      },
      {
        question: "退出后如何重新登录？",
        answer: "直接点击头像区域，会弹出一个提示框，确认后即可登录",
        images: [
          "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/login6.png",
          "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/login7.png",
        ],
      },
    ],
  },
  {
    title: "品牌",
    qaList: [
      {
        question: "如何选择品牌？",
        answer:
          "如果没有创建过烟盒，可以点击首页的‘添加一个烟盒’按钮，在“添加烟盒”页先选择类型，再从品牌列表中选择；卡片展示名称与默认/最近价格。",
        images: ["https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/brand1.png"],
      },
      {
        question: "如何创建自定义品牌？",
        answer:
          "可以先选择类型，卷烟或者电子烟，再点击加号打开弹窗，填写名称、默认价格与规格（卷烟根数/电子烟烟弹与口数），保存后即可选择。",
        images: ["https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/brand2.png","https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/brand3.png"],
      },
      {
        question: "如何编辑或删除品牌？",
        answer:
          "在品牌卡片内可编辑或删除；删除会二次确认，若为当前选中品牌将清空选择。",
        images: ["https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/brand4.png"],
      }
    ],
  },
  {
    title: "创建烟盒",
    qaList: [
      {
        question: "如何创建烟盒？",
        answer:
          "选择类型与品牌后，点击“添加烟盒”，在弹窗中填写昵称与价格，确认创建。昵称可选，如果不填写昵称则默认使用品牌的名称",
        images: ["https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/box1.png","https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/box2.png"],
      },
    ],
  },
  {
    title: "快速记录与撤销",
    alert: "",
    qaList: [
      {
        question: "快速记录条如何使用？",
        answer:
          "底部常驻条包含：云吸入口、当前烟盒切换区、步进器与记录按钮；单位随类型自动切换（根/口）。",
        images: ["https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/index1.png"],
      },
      {
        question: "如何调整记录数量？",
        answer: "步进器短按±1，长按连续调整。",
        images: [],
      },
      {
        question: "可以撤销吗？",
        answer: "记录成功后会出现约2秒“撤销”入口，可一键撤销上次记录。",
        images: ["https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/index1.png"],
      },
      {
        question: "超过每日限制怎么办？",
        answer: "若开启每日限制，超限会强震动并弹出确认，确认后可继续记录。",
        images: [],
      },
      {
        question: "云吸拦截提示",
        answer:
          "最近1小时若进行过“云吸”，再记录会提示去云吸或仍要记录，用于节律引导。",
        images: [],
      },
    ],
  },
  {
    title: "卡片操作（选择/编辑/完成/删除）",
    qaList: [
      {
        question: "如何选择当前烟盒？",
        answer: "点击卡片即可高亮为当前；进入首页会默认选中第一个。也可以通过底部功能栏，左右滑动名字区域来切换",
        image: "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/card1.png",
      },
      {
        question: "如何编辑烟盒？",
        answer:
          "长按卡片打开编辑弹窗，可修改价格、昵称与规格；规格修改具备越界校验与友好提示。",
        image: "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/card2.png",
      },
      {
        question: "如何删除烟盒？",
        answer: "右滑卡片点击“删除”，会进行二次确认。",
        image: "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/card3.png",
      },
      {
        question: "如何完成烟盒？",
        answer:
          "左滑卡片点击“完成”。电子烟完成将推进当前烟弹并在必要时完成整盒（不因口数自动完成）。",
        image: "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/card4.png",
      },
    ],
  },
  {
    title: "日历与统计",
    qaList: [
      {
        question: "如何按日或区间查看？",
        answer:
          "在“日历详情”按日期或区间查看卷烟/电子烟数量、记录次数与云吸计数。",
        image: "",
      },
      {
        question: "热力颜色代表什么？",
        answer: "开启每日限制后，未超限显示绿色，超限显示深红。",
        image: "",
      },
      {
        question: "月度统计与日均",
        answer: "展示当月卷烟/电子烟合计、花费、记录次数与日均指标。",
        image: "",
      },
      {
        question: "如何导出统计（小程序）",
        answer: "微信小程序端支持导出统计图片到相册，首次需授权“保存到相册”。",
        image: "",
      },
    ],
  },
  {
    title: "设置",
    qaList: [
      {
        question: "快速记录预设",
        answer: "可分别设置卷烟与电子烟的常用增量。",
        image: "",
      },
      {
        question: "活跃烟盒上限",
        answer: "限制同时进行中的烟盒数量，超过会提示。",
        image: "",
      },
      {
        question: "个性化主题",
        answer: "随主题切换适配深浅色方案。",
        image: "",
      },
    ],
  },
  {
    title: "成就与收藏",
    qaList: [
      {
        question: "如何解锁成就？",
        answer:
          "持续记录成就累积，“我的”页入口显示未读红点，可进入查看与同步。",
        image: "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/achievements.png",
      },
      {
        question: "我抽过的烟",
        answer: "在“我抽过的烟”查看收藏/历史品牌。",
        image: "https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/brandbox.png",
      },
    ],
  },
  {
    title: "常见问题",
    qaList: [
      {
        question: "滑动不灵怎么办？",
        answer: "尽量在卡片区域横向滑动，系统已降低触发阈值以提升识别。",
        image: "",
      },
      {
        question: "图片加载失败？",
        answer: "请检查图片链接与网络。",
        image: "",
      },
    ],
  },
]);

const currentTopic = computed(
  () => helpTopics.value[currentTopicIndex.value] || null
);

const setCurrentTopic = (index) => {
  const i = Number(index);
  if (Number.isInteger(i) && i >= 0 && i < helpTopics.value.length) {
    currentTopicIndex.value = i;
  }
};

const onQaImageError = (topicIdx, qaIdx, imgIdx) => {
  try {
    const t = helpTopics.value[topicIdx];
    if (!t || !t.qaList || !t.qaList[qaIdx]) return;
    if (Array.isArray(t.qaList[qaIdx].images) && typeof imgIdx === "number") {
      t.qaList[qaIdx].images.splice(imgIdx, 1);
    } else {
      t.qaList[qaIdx].image = "";
    }
  } catch (e) {}
};

const onTopicImageError = (topicIdx) => {
  try {
    const t = helpTopics.value[topicIdx];
    if (!t) return;
    t.image = "";
  } catch (e) {}
};
</script>

<style lang="scss" scoped>
.help-center {
  height: 100vh;
  background-color: var(--color-bg-page);
  padding: $app-spacing-md;
  display: flex;
  flex-direction: column;
  box-sizing: border-box; /* 防止 padding 导致总体高度超过视口 */
  overflow: hidden; /* 禁止整页滚动，仅内部滚动 */
}

.help-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  margin-bottom: 0; /* 避免额外外边距推高整体导致页面滚动 */
  min-height: 0; /* 关键：允许内部flex子项正确收缩 */
}

.sidebar {
  width: 240rpx;
  background-color: var(--color-bg-section);
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
  height: 100%;
}
.sidebar-item {
  padding: $app-spacing-md;
  border-bottom: 1px solid var(--color-border);
}
.sidebar-item:active {
  background-color: var(--color-bg-card);
}
.sidebar-item.active {
  background-color: var(--color-primary);
}
.sidebar-item.active .sidebar-item-text {
  color: var(--color-text-white);
  font-weight: 600;
}
.sidebar-item .sidebar-item-text {
  font-size: $app-font-size-sm;
  color: var(--color-text-primary);
  text-align: center;
  line-height: 1.4;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--color-bg-card);
  min-height: 0; /* 关键：使scroll-view按父高度约束 */
}
.content-scroll {
  flex: 1;
  width: 100%;
  height: 100%; /* 关键：让scroll-view有明确高度 */
}
.topic-content {
  padding: $app-spacing-lg;
}

.alert-box {
  display: flex;
  align-items: flex-start;
  gap: $app-spacing-sm;
  background-color: rgba(255, 151, 106, 0.08);
  border: 1px solid rgba(255, 151, 106, 0.2);
  border-radius: $app-card-radius;
  padding: $app-spacing-md;
  margin-bottom: $app-spacing-lg;
}
.alert-icon {
  color: #ff976a;
  font-size: $app-font-size-base;
  margin-top: 2rpx;
}
.alert-text {
  color: #ff976a;
  font-size: $app-font-size-sm;
  line-height: 1.5;
}

.qa-item {
  margin-bottom: $app-spacing-xl;
}
.qa-item:last-child {
  margin-bottom: 0;
}
.question {
  display: flex;
  align-items: flex-start;
  gap: $app-spacing-sm;
  margin-bottom: $app-spacing-sm;
}
.question-icon {
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  font-size: $app-font-size-xs;
  font-weight: 700;
  color: var(--color-text-white);
  background-color: var(--color-primary);
  border-radius: 9999rpx;
  flex-shrink: 0;
}
.question-text {
  font-size: $app-font-size-lg;
  font-weight: 600;
  color: var(--color-text-primary);
  line-height: 1.5;
}

.answer {
  display: flex;
  align-items: flex-start;
  gap: $app-spacing-sm;
}
.answer-icon {
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  font-size: $app-font-size-xs;
  font-weight: 700;
  color: var(--color-text-white);
  background-color: var(--color-accent);
  border-radius: 9999rpx;
  flex-shrink: 0;
}
.answer-text {
  color: var(--color-text-secondary);
  font-size: $app-font-size-base;
  line-height: 1.6;
}

.qa-image-section {
  margin-top: $app-spacing-md;
}
.image-container {
  border-radius: $app-card-radius;
  overflow: hidden;
  border: 1px solid var(--color-border);
  background-color: var(--color-bg-section);
  padding: $app-spacing-sm;
}
.help-image {
  width: 100%;
  display: block;
  border-radius: $app-button-radius;
}

.image-section {
  margin-top: $app-spacing-lg;
}

.customer-service-section {
  padding: $app-spacing-lg $app-spacing-md $app-spacing-xl $app-spacing-md;
  text-align: center;
  flex-shrink: 0; /* 底部固定在视口内 */
}
.service-hint {
  color: var(--color-text-muted);
  margin-bottom: $app-spacing-sm;
}
.customer-service-button {
  background-color: var(--color-primary);
  color: var(--color-text-white);
  border: none;
  border-radius: $app-button-radius;
  height: 96rpx;
  line-height: 96rpx;
  padding: 0 $app-spacing-lg;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.service-icon {
  font-size: $app-font-size-lg;
  margin-right: $app-spacing-xs;
  line-height: 1;
}
.service-text {
  font-size: $app-font-size-base;
}
</style>
