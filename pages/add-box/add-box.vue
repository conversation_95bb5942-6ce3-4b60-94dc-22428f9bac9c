<template>
  <view class="add-box-page" :class="currentTheme">
    <view class="form-container card">
      <view class="form-header">
        <text class="form-title">添加新烟盒</text>
        <text class="form-subtitle">请选择品牌和填写相关信息</text>
      </view>

      <view class="form-content">
        <!-- 烟草类型选择 -->
        <view class="form-section">
          <text class="section-title">烟草类型</text>
          <view class="type-selector">
            <view 
              class="type-option" 
              :class="{ active: selectedType === 'cigarette' }"
              @click="selectType('cigarette')"
            >
              <view class="iconfont type-icon">&#xeb98;</view>
              <text class="type-name">传统卷烟</text>
              <text class="type-desc">20根/盒</text>
            </view>
            <view 
              class="type-option" 
              :class="{ active: selectedType === 'electronic' }"
              @click="selectType('electronic')"
            >
              <view class="iconfont type-icon">&#xeb9c;</view>
              <text class="type-name">电子烟</text>
              <text class="type-desc">3颗烟弹/盒</text>
            </view>
          </view>
        </view>

        <!-- 品牌选择 -->
        <view class="form-section">
          <view class="brand-header">
            <text class="section-title">选择品牌</text>
            <view class="brand-add-btn" @click="showCustomBrandModal" aria-label="添加自定义品牌">
              <text class="iconfont">&#xe87a;</text>
            </view>
          </view>
          <view class="brand-grid">
            <view 
              class="brand-item" 
              v-for="brand in filteredBrands" 
              :key="brand.id"
              :class="{ active: selectedBrandId === brand.id }"
              @click="selectBrand(brand)"
            >
              <view class="brand-image">
                <image :src="brand.image" mode="aspectFit" class="brand-logo"></image>
              </view>
              <text class="brand-name">{{ brand.name }}</text>
              <text class="brand-price">{{ formatCurrency(getLastPrice(brand.id) || brand.defaultPrice) }}</text>
              <view class="brand-actions">
                <button class="btn-mini" @click.stop="openEditBrandModal(brand)">编辑</button>
                <button class="btn-mini danger" @click.stop="confirmRemoveBrand(brand)">删除</button>
              </view>
            </view>
          </view>
        </view>

        <!-- 移除页面内联的创建表单，改用居中弹窗 -->
      </view>

      <transition name="fade-up">
        <view class="form-footer" v-if="selectedBrand">
          <button class="btn-cancel" @click="goBack">取消</button>
          <button 
            class="btn-confirm btn-primary" 
            @click="openCreateBoxModal"
            :disabled="!selectedBrand"
          >
            添加烟盒
          </button>
        </view>
      </transition>
    </view>

    <!-- 自定义品牌/编辑品牌 弹窗 -->
    <CommonModal 
      ref="customBrandPopup" 
      position="center"
      :mask="true"
      :maskClosable="true"
      :animation="false"
      :zIndex="1000"
      :showClose="true"
      :showFooter="false"
      :title="brandEditorMode === 'create' ? '添加自定义品牌' : '编辑品牌'"
    >
      <view class="custom-brand-modal card">
        
        <view class="modal-form">
          <view class="form-row">
            <text class="form-label">品牌名称</text>
            <input 
              class="form-input" 
              v-model="brandForm.name" 
              placeholder="请输入品牌名称"
              maxlength="20"
            />
          </view>
          
          <view class="form-row">
            <text class="form-label">默认价格</text>
            <input 
              class="form-input" 
              type="digit" 
              v-model.number="brandForm.defaultPrice" 
              placeholder="请输入默认价格"
            />
            <text class="form-unit">元</text>
          </view>

          <view class="form-row">
            <text class="form-label">类型</text>
            <input class="form-input" :value="brandForm.type === 'cigarette' ? '卷烟' : '电子烟'" disabled />
          </view>

          <view v-if="brandForm.type === 'cigarette'">
            <view class="form-row">
              <text class="form-label">每盒根数</text>
              <input 
                class="form-input" 
                type="number" 
                v-model.number="brandForm.defaultSpecs.cigarette.count" 
                :placeholder="`默认${userSettings.value?.defaultCigaretteCount || 20}`"
              />
              <text class="form-unit">根</text>
            </view>
          </view>
          <view v-else>
            <view class="form-row">
              <text class="form-label">每盒烟弹数</text>
              <input 
                class="form-input" 
                type="number" 
                v-model.number="brandForm.defaultSpecs.electronic.cartridges" 
                placeholder="默认3"
              />
              <text class="form-unit">颗</text>
            </view>
            <view class="form-row">
              <text class="form-label">每颗烟弹口数</text>
              <input 
                class="form-input" 
                type="number" 
                v-model.number="brandForm.defaultSpecs.electronic.puffsPerCartridge" 
                :placeholder="`默认${userSettings.value?.defaultElectronicPuffs || 300}`"
              />
              <text class="form-unit">口</text>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="btn-cancel" @click="closeCustomBrandModal">取消</button>
          <button 
            class="btn-confirm btn-primary" 
            @click="submitBrand"
            :disabled="!brandForm.name || !brandForm.defaultPrice"
          >
            {{ brandEditorMode === 'create' ? '添加' : '保存' }}
          </button>
        </view>
      </view>
    </CommonModal>

    <!-- 创建烟盒弹窗（居中） -->
    <CommonModal 
      ref="createBoxPopup"
      position="center"
      :mask="true"
      :maskClosable="true"
      :animation="true"
      :zIndex="1000"
      :showClose="true"
      :showFooter="false"
      title="创建烟盒"
    >
      <view class="create-box-modal card">
        <view class="modal-form">
          <view class="form-row">
            <text class="form-label">昵称 (可选)</text>
            <input 
              class="form-input" 
              v-model="boxNickname" 
              placeholder="如：办公室的、家里的"
              maxlength="10"
            />
          </view>
          <view class="form-row">
            <text class="form-label">价格</text>
            <input 
              class="form-input" 
              type="digit" 
              v-model.number="boxPrice" 
              placeholder="请输入价格"
            />
            <text class="form-unit">元</text>
          </view>
          
        </view>
        <view class="modal-footer">
          <button 
            class="btn-confirm btn-primary" 
            @click="addBox"
            :disabled="!canSubmit"
          >
            创建
          </button>
        </view>
      </view>
    </CommonModal>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import CommonModal from '../../components/CommonModal.vue'
import { useTobaccoStore } from '../../composables/useTobaccoStore.js'
import { useUserSettings } from '../../composables/useUserSettings.js'
import smokingApi from '../../utils/smoking.js'
import { useTheme } from '../../composables/useTheme.js'
import { formatCurrency } from '../../utils/formatters.js'

// 主题
const { currentTheme } = useTheme()

// 状态管理
const { addTobaccoBox, initializeData, activeBoxes } = useTobaccoStore()
const { userSettings, initializeUserData } = useUserSettings()

// 表单数据
const selectedType = ref('cigarette')
const selectedBrandId = ref('')
const selectedBrand = ref(null)
const boxNickname = ref('')
const boxPrice = ref(0)
// 电子烟创建烟盒不再单独输入口数，以下状态可移除
// const puffsPerCartridge = ref(300)
// 计算默认规格（用户默认 > 品牌默认 > 兜底，仅用于默认填充）
const computeDefaultCigaretteTotal = (brand) => {
  const userDefault = Number(userSettings.value?.defaultCigaretteCount) || 0
  if (userDefault > 0) return userDefault
  try {
    const brandDefault = Number(brand?.defaultSpecs?.cigarette?.count) || 0
    if (brandDefault > 0) return brandDefault
  } catch (_) {}
  return 20
}

// 电子烟默认口数计算函数仅用于默认填充（UI 提示），现已不再需要
// const computeDefaultPuffsPerCartridge = (brand) => {
//   const userDefault = Number(userSettings.value?.defaultElectronicPuffs) || 0
//   if (userDefault > 0) return userDefault
//   try {
//     const brandDefault = Number(brand?.defaultSpecs?.electronic?.puffsPerCartridge) || 0
//     if (brandDefault > 0) return brandDefault
//   } catch (_) {}
//   return 300
// }


// 品牌数据
const brands = ref([])

// 品牌编辑/新增
const customBrandPopup = ref()
const createBoxPopup = ref()
const brandEditorMode = ref('create') // 'create' | 'edit'
const editingBrandId = ref('')
const brandForm = ref({
  name: '',
  type: 'cigarette',
  defaultPrice: 0,
  defaultSpecs: { cigarette: { count: 20 }, electronic: { cartridges: 3, puffsPerCartridge: 300 } }
})

// 计算属性
const filteredBrands = computed(() => {
  return brands.value.filter(brand => brand.type === selectedType.value && brand.isVirtual !== true)
})

const canSubmit = computed(() => {
  return selectedBrand.value && boxPrice.value > 0
})

// 页面初始化
onMounted(async () => {
  await initializeUserData()
  await initializeData()
  const res = await smokingApi.getBrands({ scope: 'mine' })
  brands.value = res.success ? (res.data || []) : []
  // 初始化自定义品牌编辑表单的默认规格，优先采用用户默认
  brandForm.value.defaultSpecs.cigarette.count = Number(userSettings.value?.defaultCigaretteCount) || brandForm.value.defaultSpecs.cigarette.count
  brandForm.value.defaultSpecs.electronic.puffsPerCartridge = Number(userSettings.value?.defaultElectronicPuffs) || brandForm.value.defaultSpecs.electronic.puffsPerCartridge
})

// 方法定义
const selectType = (type) => {
  selectedType.value = type
  // 清空品牌选择
  selectedBrandId.value = ''
  selectedBrand.value = null
  boxPrice.value = 0
  // 电子烟不再在创建烟盒时单独录入口数
}

const selectBrand = (brand) => {
  selectedBrandId.value = brand.id
  selectedBrand.value = brand
  
  // 设置默认价格
  const lastPrice = getLastPrice(brand.id)
  boxPrice.value = lastPrice || brand.defaultPrice
  
  // 电子烟不再在创建烟盒时单独录入口数
}

const getLastPrice = (brandId) => {
  const boxes = activeBoxes.value || []
  const related = boxes.filter(b => b.brandId === brandId)
  if (related.length === 0) return null
  related.sort((a, b) => (b.lastUsedDate || 0) - (a.lastUsedDate || 0))
  return related[0]?.price || null
}

const addBox = async () => {
  if (!canSubmit.value) return
  
  const boxData = {
    brandId: selectedBrand.value.id,
    type: selectedType.value,
    price: boxPrice.value,
    nickname: boxNickname.value.trim(),
    specs: selectedType.value === 'cigarette' 
      ? {
          cigarette: {
            total: ((Number(selectedBrand.value?.defaultSpecs?.cigarette?.count) || 0) > 0
              ? Number(selectedBrand.value.defaultSpecs.cigarette.count)
              : 20),
            consumed: 0
          }
        }
      : {
          electronic: {
            totalCartridges: selectedBrand.value.defaultSpecs?.electronic?.cartridges || 3,
            currentCartridge: 1,
            cartridgePuffsLimit: ((Number(selectedBrand.value?.defaultSpecs?.electronic?.puffsPerCartridge) || 0) > 0
              ? Number(selectedBrand.value.defaultSpecs.electronic.puffsPerCartridge)
              : 300),
            currentCartridgePuffs: 0
          }
        }
  }
  await addTobaccoBox(boxData)
  
  uni.showToast({
    title: '添加成功',
    icon: 'success'
  })
  
  // 延迟返回，让用户看到成功提示
  setTimeout(() => {
    goBack()
  }, 1500)
}

const goBack = () => {
  uni.navigateBack()
}

// NOTE: duplicate removed; the later definition is kept

const openCreateBoxModal = () => {
  if (!selectedBrand.value) {
    uni.showToast({ title: '请先选择品牌', icon: 'none' })
    return
  }
  createBoxPopup.value.open()
}

const closeCustomBrandModal = () => {
  customBrandPopup.value.close()
  // 重置表单
  brandForm.value = { 
    name: '', 
    type: selectedType.value, 
    defaultPrice: 0, 
    defaultSpecs: { 
      cigarette: { count: Number(userSettings.value?.defaultCigaretteCount) || 20 }, 
      electronic: { cartridges: 3, puffsPerCartridge: Number(userSettings.value?.defaultElectronicPuffs) || 300 } 
    } 
  }
  brandEditorMode.value = 'create'
  editingBrandId.value = ''
}

const closeCreateBoxModal = () => {
  createBoxPopup.value.close()
}

const submitBrand = async () => {
  if (!brandForm.value.name || !(brandForm.value.defaultPrice > 0)) return
  // 规格兜底
  if (brandForm.value.type === 'cigarette') {
    const cnt = brandForm.value.defaultSpecs?.cigarette?.count
    if (!cnt || cnt <= 0) brandForm.value.defaultSpecs.cigarette.count = (Number(userSettings.value?.defaultCigaretteCount) || 20)
  } else {
    const elec = brandForm.value.defaultSpecs?.electronic || {}
    if (!elec.cartridges || elec.cartridges <= 0) brandForm.value.defaultSpecs.electronic.cartridges = 3
    if (!elec.puffsPerCartridge || elec.puffsPerCartridge <= 0) brandForm.value.defaultSpecs.electronic.puffsPerCartridge = (Number(userSettings.value?.defaultElectronicPuffs) || 300)
  }

  if (brandEditorMode.value === 'create') {
    const res = await smokingApi.addCustomBrand({
      name: brandForm.value.name,
      type: brandForm.value.type,
      defaultPrice: brandForm.value.defaultPrice,
      image: '',
      defaultSpecs: brandForm.value.defaultSpecs
    })
    if (res.success) {
      // 重新拉取我的品牌列表
      const r = await smokingApi.getBrands({ scope: 'mine' })
      brands.value = r.success ? (r.data || []) : []
      // 自动选择新品牌（用名称匹配或按返回的新增值）
      const created = (brands.value || []).find(b => b.name === brandForm.value.name && b.type === brandForm.value.type)
      if (created) selectBrand(created)
      uni.showToast({ title: '品牌添加成功', icon: 'success' })
      closeCustomBrandModal()
    } else {
      uni.showToast({ title: res.message || '添加失败', icon: 'none' })
    }
  } else {
    // 编辑
    const res = await smokingApi.updateBrand(editingBrandId.value, {
      name: brandForm.value.name,
      defaultPrice: brandForm.value.defaultPrice,
      image: '',
      defaultSpecs: brandForm.value.defaultSpecs
    })
    if (res.success) {
      const r = await smokingApi.getBrands({ scope: 'mine' })
      brands.value = r.success ? (r.data || []) : []
      // 若正在选中的品牌被编辑，刷新引用对象
      if (selectedBrand.value && selectedBrand.value.id === editingBrandId.value) {
        const fresh = brands.value.find(b => b.id === editingBrandId.value)
        if (fresh) selectBrand(fresh)
      }
      uni.showToast({ title: '保存成功', icon: 'success' })
      closeCustomBrandModal()
    } else {
      uni.showToast({ title: res.message || '保存失败', icon: 'none' })
    }
  }
}

const showCustomBrandModal = () => {
  brandEditorMode.value = 'create'
  editingBrandId.value = ''
  brandForm.value = { 
    name: '', 
    type: selectedType.value, 
    defaultPrice: 0, 
    defaultSpecs: { 
      cigarette: { count: Number(userSettings.value?.defaultCigaretteCount) || 20 }, 
      electronic: { cartridges: 3, puffsPerCartridge: Number(userSettings.value?.defaultElectronicPuffs) || 300 } 
    } 
  }
  customBrandPopup.value.open()
}

const openEditBrandModal = (brand) => {
  brandEditorMode.value = 'edit'
  editingBrandId.value = brand.id
  brandForm.value = {
    name: brand.name,
    type: brand.type,
    defaultPrice: brand.defaultPrice || 0,
    defaultSpecs: JSON.parse(JSON.stringify(brand.defaultSpecs || { cigarette: { count: 20 }, electronic: { cartridges: 3, puffsPerCartridge: 300 } }))
  }
  customBrandPopup.value.open()
}

const confirmRemoveBrand = (brand) => {
  uni.showModal({
    title: '删除品牌',
    content: '该操作不可恢复，确定删除该品牌？',
    success: async (res) => {
      if (res.confirm) {
        const r = await smokingApi.removeBrand(brand.id)
        if (r.success) {
          brands.value = (brands.value || []).filter(b => b.id !== brand.id)
          if (selectedBrandId.value === brand.id) {
            selectedBrandId.value = ''
            selectedBrand.value = null
            boxPrice.value = 0
          }
          uni.showToast({ title: '删除成功', icon: 'success' })
        } else {
          uni.showToast({ title: r.message || '删除失败', icon: 'none' })
        }
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.add-box-page {
  padding: $app-spacing-md;
  min-height: 100vh;
  background-color: var(--color-bg-page);
}

.form-container {
  padding: $app-spacing-md;
}

.form-header {
  text-align: center;
  margin-bottom: $app-spacing-lg;
}

.form-title {
  font-size: $app-font-size-xxl;
  font-weight: bold;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: $app-spacing-xs;
}

.form-subtitle {
  font-size: $app-font-size-base;
  color: var(--color-text-muted);
}

.form-section {
  margin-bottom: $app-spacing-xl;
}

.section-title {
  font-size: $app-font-size-lg;
  font-weight: bold;
  color: var(--color-text-primary);
  display: block;
}

.type-selector {
  display: flex;
  gap: $app-spacing-md;
}

.type-option {
  flex: 1;
  background-color: var(--color-bg-section);
  border: 4rpx solid var(--color-border);
  border-radius: $app-card-radius;
  padding: $app-spacing-md;
  text-align: center;
  
  &.active {
    border-color: var(--color-primary);
    background-color: transparent;
  }
  
  &:active {
    opacity: 0.7;
  }
}

.type-icon {
  font-size: 64rpx;
  margin-bottom: $app-spacing-sm;
}

.type-name {
  font-size: $app-font-size-lg;
  font-weight: bold;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: $app-spacing-xs;
}

.type-desc {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
}

.brand-grid {
  // Use flex-wrap for better multi-end compatibility than CSS Grid
  display: flex;
  flex-wrap: wrap;
  // Horizontal negative margins to balance child horizontal margins
  margin: 0 -$app-spacing-sm $app-spacing-md;
}

.brand-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $app-spacing-md;
}

.brand-add-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: var(--color-text-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $app-font-size-lg;
}

.brand-item {
  background-color: var(--color-bg-section);
  border: 4rpx solid var(--color-border);
  border-radius: $app-card-radius;
  padding: $app-spacing-sm;
  text-align: center;
  box-sizing: border-box;
  // two columns layout with consistent gutters
  width: calc(50% - #{$app-spacing-sm * 2});
  margin: 0 $app-spacing-sm $app-spacing-md;
  min-width: 0;
  overflow: hidden;
  &.active {
    border-color: var(--color-primary);
    background-color: transparent;
  }
  
  &:active {
    opacity: 0.7;
  }
}

.brand-actions {
  display: flex;
  gap: $app-spacing-xs;
  margin-top: $app-spacing-sm;
  justify-content: center;
}

.btn-mini {
  padding: 8rpx 16rpx;
  font-size: $app-font-size-xs;
  border-radius: $app-button-radius;
  background-color: var(--color-border);
  color: var(--color-text-primary);
}

.btn-mini.danger {
  background-color: transparent;
  color: var(--color-danger);
  border: 1px solid var(--color-danger);
  
  &:active {
    background-color: var(--color-danger);
    color: var(--color-text-white);
  }
}

.brand-image {
  height: 80rpx;
  margin-bottom: $app-spacing-sm;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-logo {
  width: 80rpx;
  height: 80rpx;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

// Optional: narrow screens fallback to 2 columns (H5 primarily)
@media (max-width: 360px) {
  .brand-item {
    width: calc(50% - #{$app-spacing-sm * 2});
  }
}

.brand-name {
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
  font-weight: 500;
  display: block;
  margin-bottom: $app-spacing-xs;
}

.brand-price {
  font-size: $app-font-size-sm;
  color: var(--color-secondary);
  font-weight: bold;
}

.custom-brand-btn {
  background-color: var(--color-border);
  border: 4rpx dashed var(--color-border);
  border-radius: $app-card-radius;
  padding: $app-spacing-lg;
  text-align: center;
  
  &:active {
    opacity: 0.7;
  }
}

.custom-text {
  color: var(--color-text-muted);
  font-size: $app-font-size-base;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: $app-spacing-md;
}

.form-label {
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
  margin-right: $app-spacing-sm;
  min-width: 200rpx;
}

.form-input {
  flex: 1;
  padding: $app-spacing-sm;
  border: 1px solid var(--color-border);
  border-radius: $app-input-radius;
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
}
.form-input::placeholder { color: var(--color-text-muted); }

.form-unit {
  margin-left: $app-spacing-sm;
  color: var(--color-text-muted);
  font-size: $app-font-size-base;
}

.form-footer {
  display: flex;
  gap: $app-spacing-md;
  margin-top: $app-spacing-lg;
}

.btn-cancel {
  flex: 1;
  background-color: var(--color-cancel-bg);
  color: var(--color-cancel-text);
  border: 1px solid var(--color-cancel-border);
  border-radius: $app-button-radius;
  padding: $app-spacing-sm $app-spacing-lg;
  font-size: $app-font-size-base;
}

.btn-confirm {
  flex: 2;
  padding: $app-spacing-sm $app-spacing-lg;
  font-size: $app-font-size-base;
  
  &:disabled {
    background-color: var(--color-text-muted);
  }
}

/* 更高优先级：当主按钮处于禁用态时，在暗色与亮色均使用中性灰底，避免发白突兀 */
.modal-footer .btn-confirm.btn-primary[disabled],
.modal-footer .btn-confirm.btn-primary:disabled {
  background-color: var(--color-disabled-bg) !important;
  color: var(--color-disabled-text) !important;
  border: 1px solid var(--color-disabled-border) !important;
}

.custom-brand-modal {
  width: 100%;
  box-sizing: border-box;
  padding: $app-spacing-md;
}

.create-box-modal {
  width: 100%;
  box-sizing: border-box;
  padding: $app-spacing-md;
}

.modal-title {
  font-size: $app-font-size-xl;
  font-weight: bold;
  color: var(--color-text-primary);
  text-align: center;
  display: block;
  margin-bottom: $app-spacing-lg;
}

.modal-form {
  margin-bottom: $app-spacing-lg;
}

.modal-footer {
  display: flex;
  gap: $app-spacing-md;
}

/* 缩小两个弹窗底部按钮尺寸，避免继承页面大按钮样式 */
.modal-footer .btn-cancel,
.modal-footer .btn-confirm {
  flex: 0 0 auto;
  padding: $app-spacing-sm $app-spacing-xl;
  font-size: $app-font-size-base;
}

/* Appear/disappear transition for bottom actions */
.fade-up-enter-active,
.fade-up-leave-active {
  transition: all 200ms ease;
}
.fade-up-enter-from,
.fade-up-leave-to {
  opacity: 0;
  transform: translateY(8rpx);
}
</style>