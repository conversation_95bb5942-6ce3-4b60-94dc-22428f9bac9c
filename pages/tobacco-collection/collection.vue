<template>
  <view class="collection-page" :class="currentTheme">
    <!-- 搜索和筛选 -->
    <view class="search-section card">
      <view class="search-bar">
        <uni-search-bar
          v-model="searchKeyword"
          placeholder="搜索品牌名称"
          :radius="8"
          cancelButton="auto"
          @input="onSearch"
          @confirm="onSearch"
          @clear="onSearch"
          @cancel="onCancel"
        />
      </view>
      
      <view class="filter-tabs">
        <view 
          class="filter-tab" 
          :class="{ active: selectedFilter === 'all' }"
          @click="setFilter('all')"
        >
          <text>全部</text>
        </view>
        <view 
          class="filter-tab" 
          :class="{ active: selectedFilter === 'cigarette' }"
          @click="setFilter('cigarette')"
        >
          <text>卷烟</text>
        </view>
        <view 
          class="filter-tab" 
          :class="{ active: selectedFilter === 'electronic' }"
          @click="setFilter('electronic')"
        >
          <text>电子烟</text>
        </view>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-section card">
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{ collectionStats.totalBrands }}</text>
          <text class="stat-label">抽过品牌</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ collectionStats.totalBoxes }}</text>
          <text class="stat-label">总共盒数</text>
        </view>
        <view class="stat-item">
            <text class="stat-value">{{ formatCurrency(collectionStats.totalSpent) }}</text>
          <text class="stat-label">总花费</text>
        </view>
      </view>
    </view>

    <!-- 品牌收藏列表 -->
    <view class="collection-grid" v-if="filteredCollection.length > 0">
      <view 
        class="collection-item card" 
        v-for="item in filteredCollection" 
        :key="item.brand.id"
        @click="showBrandDetail(item)"
      >
        <view class="brand-header">
          <view class="brand-image">
            <image :src="item.brand.image" mode="aspectFit" class="brand-logo"></image>
          </view>
          <view class="brand-type-badge" :class="item.brand.type">
            {{ item.brand.type === 'cigarette' ? '卷烟' : '电子烟' }}
          </view>
        </view>
        
        <view class="brand-info">
          <text class="brand-name">{{ item.brand.name }}</text>
          <view class="brand-stats">
            <text class="stat-text">{{ item.totalBoxes }}盒</text>
            <text class="stat-text">{{ formatCurrency(item.totalSpent) }}</text>
          </view>
        </view>
        
        <view class="brand-rating">
          <view class="stars">
            <text 
              class="star" 
              v-for="i in 5" 
              :key="i"
              :class="{ filled: i <= item.personalRating }"
            >
              ★
            </text>
          </view>
        </view>
        
        <view class="brand-footer">
          <text class="last-use">最后使用: {{ formatLastUse(item.lastUseDate) }}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <view class="iconfont empty-icon">&#xeb98;</view>
      <text class="empty-text">{{ getEmptyText() }}</text>
      <!-- <button class="btn-primary" @click="goToAddBox">添加第一个烟盒</button> -->
    </view>

    <!-- 品牌详情弹窗 -->
    <CommonModal 
      class="brand-modal"
      ref="brandDetailPopup" 
      position="center"
      :mask="true"
      :maskClosable="true"
      :animation="true"
      :zIndex="1000"
      :showClose="true"
      :showFooter="false"
      :title="selectedBrandDetail ? selectedBrandDetail.brand.name : ''"
      @close="onBrandModalClosed"
    >
      <view class="brand-detail-modal" v-if="selectedBrandDetail">
        <view class="detail-content">
          <view class="detail-section">
            <text class="detail-title">使用统计</text>
            <view class="detail-stats">
              <view class="detail-stat">
                <text class="detail-label">总盒数</text>
                <text class="detail-value">{{ selectedBrandDetail.totalBoxes }}盒</text>
              </view>
              <view class="detail-stat">
                <text class="detail-label">总花费</text>
                <text class="detail-value">¥{{ selectedBrandDetail.totalSpent }}</text>
              </view>
              <view class="detail-stat">
                <text class="detail-label">首次尝试</text>
                <text class="detail-value">{{ formatDateOnly(selectedBrandDetail.firstTryDate) }}</text>
              </view>
              <view class="detail-stat">
                <text class="detail-label">最后使用</text>
                <text class="detail-value">{{ formatDateOnly(selectedBrandDetail.lastUseDate) }}</text>
              </view>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="detail-title">个人评价</text>
            <view class="rating-display">
              <view class="stars large clickable">
                <text 
                  class="star" 
                  v-for="i in 5" 
                  :key="i"
                  :class="{ filled: i <= detailRating }"
                  @click.stop="setRating(i)"
                >
                  ★
                </text>
              </view>
              <text class="rating-text">{{ detailRating }}/5分</text>
            </view>
            <view class="notes-editor">
              <textarea 
                class="notes-textarea"
                v-model="detailNotes"
                :maxlength="500"
                placeholder="写点体验或备注（最多500字）"
              />
              <view class="notes-count">{{ (detailNotes || '').length }}/500</view>
            </view>
            <view class="detail-actions">
              <!-- <button class="btn-secondary" @click="selectForNewBox">选择此品牌开盒</button> -->
              <button class="btn-primary" @click="saveBrandPersonalMeta">保存</button>
            </view>
          </view>
          
          
        </view>
      </view>
    </CommonModal>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import CommonModal from '../../components/CommonModal.vue'
import smokingApi from '../../utils/smoking.js'
import { formatDateOnly, getRelativeTime } from '../../utils/dateUtils.js'
import { useTheme } from '../../composables/useTheme.js'
import { formatCurrency } from '../../utils/formatters.js'

// 主题
const { currentTheme } = useTheme()

// 数据状态
const searchKeyword = ref('')
const selectedFilter = ref('all')
const collection = ref([])
const brands = ref([])
const selectedBrandDetail = ref(null)
const detailRating = ref(0)
const detailNotes = ref('')
const brandDetailPopup = ref()

// 页面初始化
onMounted(() => {
  loadCollection()
})

// 计算属性
const filteredCollection = computed(() => {
  let result = collection.value

  // 按类型筛选
  if (selectedFilter.value !== 'all') {
    result = result.filter(item => item.brand.type === selectedFilter.value)
  }

  // 按关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase()
    result = result.filter(item => 
      item.brand.name.toLowerCase().includes(keyword)
    )
  }

  // 按使用频率排序
  return result.sort((a, b) => b.totalBoxes - a.totalBoxes)
})

const collectionStats = computed(() => {
  const stats = {
    totalBrands: collection.value.length,
    totalBoxes: 0,
    totalSpent: 0
  }

  collection.value.forEach(item => {
    stats.totalBoxes += item.totalBoxes
    stats.totalSpent += item.totalSpent
  })

  stats.totalSpent = Math.round(stats.totalSpent * 100) / 100

  return stats
})

// 方法定义
const loadCollection = async () => {
  const res = await smokingApi.getBrands({ scope: 'mine' })
  brands.value = res.success ? (res.data || []) : []
  // 从真实数据聚合收藏：基于已使用过的品牌统计（根据盒子与记录汇总）
  const [boxesRes, recordsRes] = await Promise.all([
    smokingApi.listBoxes({ activeOnly: false }),
    smokingApi.listRecords({})
  ])
  const boxes = boxesRes.success ? (boxesRes.data || []) : []
  const records = recordsRes.success ? (recordsRes.data || []) : []
  const statMap = {}
  // 盒子花费直接累加价格（简化，严格应按记录花费模型，这里以记录计算）
  // 以记录驱动统计：统计每条记录归属的品牌
  const boxIdToBox = Object.fromEntries(boxes.map(b => [b.id, b]))
  records.forEach(r => {
    const box = boxIdToBox[r.boxId]
    if (!box) return
    const brandId = box.brandId
    if (!statMap[brandId]) statMap[brandId] = { totalBoxes: 0, totalSpent: 0, firstTryDate: null, lastUseDate: null, count: 0 }
    statMap[brandId].count += 1
    // 花费按记录的 spent 字段累计（与首页/我的保持一致）
    const spent = typeof r.spent === 'number' ? r.spent : 0
    statMap[brandId].totalSpent += spent
    if (!statMap[brandId].firstTryDate || r.timestamp < statMap[brandId].firstTryDate) statMap[brandId].firstTryDate = r.timestamp
    if (!statMap[brandId].lastUseDate || r.timestamp > statMap[brandId].lastUseDate) statMap[brandId].lastUseDate = r.timestamp
  })
  // 盒数统计：按盒子归属品牌计数
  boxes.forEach(b => {
    const brandId = b.brandId
    if (!statMap[brandId]) statMap[brandId] = { totalBoxes: 0, totalSpent: 0, firstTryDate: null, lastUseDate: null, count: 0 }
    statMap[brandId].totalBoxes += 1
  })
  // 组装集合
  collection.value = Object.keys(statMap).map(brandId => {
    const brand = brands.value.find(b => b.id === brandId)
    if (!brand) return null
    const s = statMap[brandId]
    return {
      brandId,
      brand,
      totalBoxes: s.totalBoxes,
      totalSpent: Math.round(s.totalSpent * 100) / 100,
      firstTryDate: s.firstTryDate,
      lastUseDate: s.lastUseDate,
      personalRating: brand.personalRating || 0,
      personalNotes: brand.personalNotes || ''
    }
  }).filter(Boolean)
}

const onSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const onCancel = () => {
  searchKeyword.value = ''
  onSearch()
}

const setFilter = (filter) => {
  selectedFilter.value = filter
}

const getEmptyText = () => {
  if (searchKeyword.value.trim()) {
    return `没有找到"${searchKeyword.value}"相关的品牌`
  }
  
  switch (selectedFilter.value) {
    case 'cigarette':
      return '还没有使用过卷烟品牌'
    case 'electronic':
      return '还没有使用过电子烟品牌'
    default:
      return '还没有收藏任何品牌'
  }
}

const formatLastUse = (date) => {
  return getRelativeTime(date)
}

const showBrandDetail = (item) => {
  selectedBrandDetail.value = item
  detailRating.value = item.personalRating || 0
  detailNotes.value = item.personalNotes || ''
  brandDetailPopup.value.open()
}

const closeBrandDetail = () => {
  brandDetailPopup.value.close()
  selectedBrandDetail.value = null
  detailRating.value = 0
  detailNotes.value = ''
}

const onBrandModalClosed = () => {
  selectedBrandDetail.value = null
  detailRating.value = 0
  detailNotes.value = ''
}

// const selectForNewBox = () => {
//   // 将选中的品牌信息传递给添加烟盒页面
//   const brand = selectedBrandDetail.value.brand
  
//   uni.navigateTo({
//     url: `/pages/add-box/add-box?brandId=${brand.id}&type=${brand.type}`
//   })
  
//   closeBrandDetail()
// }

const setRating = (val) => {
  if (!Number.isInteger(val)) return
  // 二次点击相同分数清零（可选逻辑）
  detailRating.value = (detailRating.value === val) ? 0 : val
}

const saveBrandPersonalMeta = async () => {
  if (!selectedBrandDetail.value) return
  const brandId = selectedBrandDetail.value.brand.id
  const rating = detailRating.value
  const notes = (detailNotes.value || '').trim()
  // 前端校验
  if (!Number.isInteger(rating) || rating < 0 || rating > 5) {
    uni.showToast({ title: '评分需为0~5的整数', icon: 'none' })
    return
  }
  if (notes.length > 500) {
    uni.showToast({ title: '备注最多500字', icon: 'none' })
    return
  }
  const res = await smokingApi.updateBrand(brandId, { personalRating: rating, personalNotes: notes })
  if (res.success) {
    // 更新当前弹窗与集合项
    selectedBrandDetail.value.personalRating = rating
    selectedBrandDetail.value.personalNotes = notes
    // 同步更新 collection 列表中对应项（以 brandId 匹配）
    const idx = collection.value.findIndex(x => x.brandId === selectedBrandDetail.value.brandId)
    if (idx !== -1) {
      collection.value[idx] = { ...collection.value[idx], personalRating: rating, personalNotes: notes }
    }
    uni.showToast({ title: '已保存', icon: 'success' })
  } else {
    uni.showToast({ title: res.message || '保存失败', icon: 'none' })
  }
}

// const goToAddBox = () => {
//   uni.navigateTo({
//     url: '/pages/add-box/add-box'
//   })
// }
</script>

<style lang="scss" scoped>
.collection-page {
  padding: $app-spacing-md;
  min-height: 100vh;
  background-color: var(--color-bg-page);
}

.search-section {
  padding: $app-spacing-lg;
  margin-bottom: $app-spacing-md;
}

.search-bar {
  position: relative;
  margin-bottom: $app-spacing-md;
}
.search-bar :deep(.uni-searchbar__box) { background-color: var(--color-bg-section) !important; }
.search-bar :deep(.uni-searchbar__box-search-input) { color: var(--color-text-primary) !important; }
.search-bar :deep(.uni-searchbar__text-placeholder) { color: var(--color-text-muted) !important; }
.search-bar :deep(.uni-searchbar__box-icon-search uni-icons) { color: var(--color-text-muted) !important; }



.filter-tabs {
  display: flex;
  gap: $app-spacing-sm;
}
.filter-tabs :deep(.uni-searchbar__box) { background-color: var(--color-bg-section) !important; }

.filter-tab {
  flex: 1;
  text-align: center;
  padding: $app-spacing-sm;
  background-color: var(--color-bg-section);
  border-radius: $app-button-radius;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  
  &.active {
    background-color: var(--color-primary);
    color: var(--color-text-white);
    border-color: var(--color-primary);
  }
  
  &:active {
    opacity: 0.7;
  }
}

.stats-section {
  padding: $app-spacing-lg;
  margin-bottom: $app-spacing-md;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: $app-font-size-xl;
  font-weight: bold;
  color: var(--color-primary);
  display: block;
  margin-bottom: $app-spacing-xs;
}

.stat-label {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
}

.collection-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $app-spacing-md;
}

.collection-item {
  padding: $app-spacing-md;
  
  &:active {
    opacity: 0.7;
  }
}

.brand-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $app-spacing-sm;
}

.brand-image {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-logo {
  max-width: 100%;
  max-height: 100%;
}

.brand-type-badge {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: $app-font-size-xs;
  color: var(--color-text-white);
  
  &.cigarette {
    background-color: var(--color-secondary);
  }
  
  &.electronic {
    background-color: var(--color-accent);
  }
}

.brand-info {
  margin-bottom: $app-spacing-sm;
}

.brand-name {
  font-size: $app-font-size-lg;
  font-weight: bold;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: $app-spacing-xs;
}

.brand-stats {
  display: flex;
  gap: $app-spacing-sm;
}

.stat-text {
  font-size: $app-font-size-sm;
  color: var(--color-text-secondary);
}

.brand-rating {
  margin-bottom: $app-spacing-sm;
}

.stars {
  display: flex;
  gap: 4rpx;
  
  &.large .star {
    font-size: 48rpx;
  }
  &.clickable .star { cursor: pointer; }
}

.star {
  font-size: $app-font-size-lg;
  color: var(--color-border);
  
  &.filled {
    color: var(--color-secondary);
  }
}

.last-use {
  font-size: $app-font-size-xs;
  color: var(--color-text-muted);
}

.empty-state {
  text-align: center;
  padding: $app-spacing-xl;
  margin-top: $app-spacing-xl;
}

.empty-icon {
  font-size: 128rpx;
  margin-bottom: $app-spacing-lg;
}

.empty-text {
  font-size: $app-font-size-lg;
  color: var(--color-text-muted);
  margin-bottom: $app-spacing-lg;
  display: block;
}

.brand-detail-modal {
  background-color: var(--color-bg-card);
  border-radius: $app-card-radius;
  max-height: 75vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $app-spacing-lg;
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  font-size: $app-font-size-xl;
  font-weight: bold;
  color: var(--color-text-primary);
}

.modal-close {
  font-size: 48rpx;
  color: var(--color-text-muted);
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-content {
  padding: $app-spacing-sm $app-spacing-md; /* 仅垂直缩小，高度更紧凑，左右保持原有视觉宽度 */
}

.detail-section {
  margin-bottom: $app-spacing-md;
}

.detail-title {
  font-size: $app-font-size-base;
  font-weight: bold;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: $app-spacing-md;
}

.detail-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: $app-spacing-md; /* 横向保持原有间距 */
  row-gap: $app-spacing-sm;    /* 纵向收紧以减小高度 */
}

.detail-stat {
  background-color: var(--color-bg-section);
  padding: $app-spacing-sm $app-spacing-md; /* 上下收紧，左右保持 */
  border-radius: $app-button-radius;
}

.detail-label {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
  display: block;
  margin-bottom: $app-spacing-xs;
}

.detail-value {
  font-size: $app-font-size-base;
  font-weight: bold;
  color: var(--color-text-primary);
}

.rating-display {
  display: flex;
  align-items: center;
  gap: $app-spacing-sm;
  margin-bottom: $app-spacing-sm;
}

.rating-text {
  font-size: $app-font-size-base;
  color: var(--color-text-secondary);
}

.personal-notes {
  font-size: $app-font-size-base;
  color: var(--color-text-secondary);
  line-height: 1.5;
  padding: $app-spacing-md;
  background-color: var(--color-bg-section);
  border-radius: $app-button-radius;
}

.detail-actions {
  display: flex;
  gap: $app-spacing-md;
  .btn-primary {
    flex: 1; /* 让保存按钮在容器中尽可能占满宽度 */
  }
}

.notes-editor {
  margin-top: $app-spacing-sm;
}

.notes-textarea {
  width: 100%;
  min-height: 160rpx;
  box-sizing: border-box;
  padding: $app-spacing-sm;
  border: 1px solid var(--color-border);
  border-radius: $app-input-radius;
  font-size: $app-font-size-base;
  background-color: var(--color-bg-section);
}

.notes-count {
  text-align: right;
  color: var(--color-text-muted);
  font-size: $app-font-size-xs;
  margin-top: $app-spacing-xs;
}

/* 仅对本弹窗实例覆盖 CommonModal 的尺寸与滚动行为 */
:deep(.brand-modal .cm-panel) {
  max-width: 96vw;
  min-width: 86vw;
}

:deep(.brand-modal .cm-panel-center) {
  overflow-y: hidden;
}

/* 底部按钮与内容的间距 */
.detail-actions {
  margin-top: $app-spacing-md;
}
</style>
