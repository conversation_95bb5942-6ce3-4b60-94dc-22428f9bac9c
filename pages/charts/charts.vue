<template>
  <view class="charts-page" :class="currentTheme">
    <!-- 顶部切换（周/月/年） -->
    <view class="toolbar card">
      <view class="segmented">
        <view :class="['seg-item', activeTab==='week' ? 'active' : '']" @click="setTab('week')">周</view>
        <view :class="['seg-item', activeTab==='month' ? 'active' : '']" @click="setTab('month')">月</view>
        <view :class="['seg-item', activeTab==='year' ? 'active' : '']" @click="setTab('year')">年</view>
      </view>
    </view>

    <!-- 第二行：周期选择（水平滚动） -->
    <scroll-view
      class="period-bar"
      scroll-x="true"
      show-scrollbar="false"
      :scroll-into-view="periodScrollIntoId"
      scroll-with-animation="true"
      upper-threshold="40"
      @scrolltoupper="handlePeriodScrollToUpper"
    >
      <view class="period-container">
        <view
          v-for="item in periodItems"
          :key="item.key"
          :id="item.key"
          :class="['period-chip', item.selected ? 'selected' : '']"
          @click="selectPeriod(item)"
        >{{ item.label }}</view>
      </view>
    </scroll-view>

    <!-- 折线图 -->
    <view class="chart-section card">
      <view class="chart-header" v-if="showRangeLabel">
        <text class="chip chip--range">{{ rangeLabel }}</text>
      </view>
      <qiun-data-charts
        ref="lineChart"
        type="line"
        :chartData="chartData"
        :opts="lineOpts"
        :reshow="reshowTick"
        @getImage="onChartImage"
      />
    </view>

    <!-- 汇总信息 -->
    <view class="summary card">
      <view class="summary-items">
        <view class="summary-item">
          <text class="label">卷烟</text>
          <text class="value primary">{{ totals.cigarettes }}</text>
          <text class="unit">根</text>
          <view>
            <text class="label small">{{ boxCounts.cigarette || 0 }} 盒</text>
          </view>
        </view>
        <view class="summary-item">
          <text class="label">电子烟</text>
          <text class="value primary">{{ totals.puffs }}</text>
          <text class="unit">口</text>
          <view>
            <text class="label small">{{ boxCounts.electronic || 0 }} 盒</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 盒子合计列表 -->
    <view class="boxes card">
      <view class="boxes-header">
        <text class="section-title">按烟盒统计</text>
      </view>
      <view v-if="boxTotals.length > 0">
        <view class="box-row" v-for="item in boxTotals" :key="item.boxId">
          <view class="box-title">
            <text class="brand">{{ item.box?.nickname || item.brand?.name || '未知' }}</text>
            <text class="brand-sub" v-if="item.box?.nickname && item.brand">({{ item.brand.name }})</text>
            <text class="tag-cloud" v-if="item.virtual || item.brand?.virtual">云</text>
          </view>
          <view class="box-values">
            <text class="value">{{ item.type === 'cigarette' ? item.cigarettes : item.puffs }}</text>
            <text class="unit">{{ item.type === 'cigarette' ? '根' : '口' }}</text>
          </view>
        </view>
      </view>
      <view class="empty" v-else>
        <text class="empty-text">该范围内无记录</text>
      </view>
    </view>
    
    <!-- 悬浮导出按钮与隐藏Canvas（仅微信小程序） -->
    <!-- #ifdef MP-WEIXIN -->
    <view class="export-fab" @click="confirmExportCharts">
      <text class="export-fab-text">导出</text>
    </view>
    <canvas
      canvas-id="chartsExportCanvas"
      id="chartsExportCanvas"
      class="export-canvas"
      :style="{ width: exportCanvasWidth + 'px', height: exportCanvasHeight + 'px' }"
    ></canvas>
    <!-- #endif -->
  </view>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useTobaccoStore } from '../../composables/useTobaccoStore.js'
import { useTheme } from '../../composables/useTheme.js'
import { useChartsData } from '../../composables/useChartsData.js'
import dayjs from '../../utils/dayjs-setup.js'

const { currentTheme } = useTheme()

// 数据源（本页私有 records，所见即所得）
const { activeBoxes, getBrand, initializeData, fetchRecords } = useTobaccoStore()
const pageRecords = ref([])
const charts = useChartsData(pageRecords, activeBoxes, getBrand)

const activeTab = ref('week')
const offset = ref(0) // 相对本周期偏移：周/月/年
// 进入页面时强制重绘图表（组件watch: reshow===true时触发）
const reshowTick = ref(false)

// 周期条滚动与加载更多控制
const periodScrollIntoId = ref('w0')
const LOAD_MORE_STEPS = { week: 20, month: 12, year: 6 }
const minWeekOffset = ref(-19)
const minMonthOffset = ref(-11)
const minYearOffset = ref(-5)
const isLoadingMorePeriods = ref(false)

// 导出画布设置（仅小程序）
// #ifdef MP-WEIXIN
const exportCanvasWidth = 750
const exportCanvasHeight = ref(0)
const pixelRatio = uni.getSystemInfoSync().pixelRatio || 1
const lineChart = ref(null)
let _chartImgResolve = null
let _chartImgReject = null
let _chartImgTimer = null
// #endif

onMounted(async () => {
  await initializeData()
  // 初次进入，仅请求当前tab+offset=0 对应范围
  try {
    const { start, end } = getCurrentRange()
    const list = await fetchRecords(start, end)
    pageRecords.value = list
  } catch (_) {}
})

onShow(() => {
  // 置为true以触发组件重绘，随后复位为false，便于下次进入再次触发
  reshowTick.value = true
  setTimeout(() => { reshowTick.value = false }, 0)
})

const model = computed(() => {
  if (activeTab.value === 'week') return charts.buildWeeklySeries(offset.value)
  if (activeTab.value === 'month') return charts.buildMonthlySeries(offset.value)
  return charts.buildYearlySeries(offset.value)
})

// X轴标签优化：月 -> 每5天；年 -> 1/3/6/9/12；周 -> 原样
const displayCategories = computed(() => {
  const cats = model.value.categories || []
  if (activeTab.value === 'month') {
    const len = cats.length
    return cats.map((_, idx) => {
      const day = idx + 1
      const marks = new Set([1,5,10,15,20,25,len])
      return marks.has(day) ? String(day) : ''
    })
  }
  if (activeTab.value === 'year') {
    return cats.map((_, idx) => {
      const m = idx + 1
      return [1,3,6,9,12].includes(m) ? String(m) : ''
    })
  }
  return cats
})

const chartData = computed(() => ({
  categories: displayCategories.value,
  series: model.value.series || []
}))

const lineOpts = computed(() => {
  const isDark = currentTheme.value === 'theme-dark'
  const axisColor = isDark ? '#9CA3AF' : '#718096'
  const gridColor = isDark ? '#374151' : '#E2E8F0'
  // 真实：蓝、绿；云吸：薄绿、薄青
  const colors = isDark
    ? ['#60A5FA', '#34D399', '#45C07A', '#2BB5A8']
    : ['#2C5282', '#48BB78', '#45C07A', '#2BB5A8']
  return {
    animation: true,
    color: colors,
    legend: {
      show: true,
      fontColor: axisColor
    },
    dataLabel: false,
    xAxis: {
      disableGrid: false,
      gridColor,
      axisLineColor: gridColor,
      axisLabelColor: axisColor
    },
    yAxis: {
      min: 0,
      gridType: 'dash',
      gridColor,
      dashLength: 4,
      splitNumber: 4,
      axisLineColor: gridColor,
      axisLabelColor: axisColor
    },
    extra: {
      line: {
        type: 'curve',
        width: 2,
        activeType: 'hollow',
        area: true,
        opacity: isDark ? 0.15 : 0.2
      }
    }
  }
})

const totals = computed(() => model.value.totals || { cigarettes: 0, puffs: 0 })
const boxTotals = computed(() => model.value.boxes || [])
const boxCounts = computed(() => model.value.boxCounts || { cigarette: 0, electronic: 0 })
const rangeLabel = computed(() => model.value.range?.label || '')
const showRangeLabel = computed(() => offset.value !== 0 && !!rangeLabel.value)

const setTab = async (tab) => {
  if (activeTab.value !== tab) {
    activeTab.value = tab
    offset.value = 0
    // 重置左侧最小偏移范围
    minWeekOffset.value = -19
    minMonthOffset.value = -11
    minYearOffset.value = -5
    // 切换后滚动到最近周期（最右侧），随后清空以避免后续渲染反复回弹
    const id = tab === 'week' ? 'w0' : (tab === 'month' ? 'm0' : 'y0')
    scrollToLatest(id)
    // 按新tab范围加载（仅所选周期）
    try {
      const { start, end } = getCurrentRange()
      const list = await fetchRecords(start, end)
      pageRecords.value = list
    } catch (_) {}
  }
}

// 第二行周期条：根据当前tab生成
const periodItems = computed(() => {
  const items = []
  if (activeTab.value === 'week') {
    // 向左可扩展，从最小周偏移到本周（0）
    for (let i = minWeekOffset.value; i <= 0; i++) {
      const o = i
      const d = dayjs().add(o, 'week')
      const curYear = dayjs().year()
      const isThis = o === 0
      const isLast = o === -1
      let label = ''
      if (isThis) label = '本周'
      else if (isLast) label = '上周'
      else label = d.year() === curYear ? `${d.week()}周` : `${d.year()}年${d.week()}周`
      items.push({ key: `w${o}`, label, offset: o, selected: offset.value === o })
    }
  } else if (activeTab.value === 'month') {
    // 向左可扩展，从最小月偏移到本月（0）
    for (let i = minMonthOffset.value; i <= 0; i++) {
      const o = i
      const d = dayjs().add(o, 'month')
      const curYear = dayjs().year()
      const isThis = o === 0
      const isLast = o === -1
      let label = ''
      if (isThis) label = '本月'
      else if (isLast) label = '上月'
      else label = d.year() === curYear ? `${d.month() + 1}月` : `${d.year()}年${d.month() + 1}月`
      items.push({ key: `m${o}`, label, offset: o, selected: offset.value === o })
    }
  } else {
    // 向左可扩展，从最旧年到本年（左旧右新）
    for (let i = minYearOffset.value; i <= 0; i++) {
      const o = i
      const y = dayjs().add(o, 'year').year()
      const label = o === 0 ? `${y}年(本年)` : `${y}年`
      items.push({ key: `y${o}`, label, offset: o, selected: offset.value === o })
    }
  }
  return items
})

const selectPeriod = async (item) => {
  if (!item) return
  offset.value = item.offset
  // 选择具体周期时按该周期范围加载
  try {
    const { start, end } = getCurrentRange(false)
    const list = await fetchRecords(start, end)
    pageRecords.value = list
  } catch (_) {}
}

// 滚动到左侧边缘时，继续向左扩展更多周期
const handlePeriodScrollToUpper = () => {
  if (isLoadingMorePeriods.value) return
  isLoadingMorePeriods.value = true

  // 记录扩展前的最左项ID，扩展后回滚到该锚点，保持视觉位置稳定
  const oldFirstId = activeTab.value === 'week'
    ? `w${minWeekOffset.value}`
    : (activeTab.value === 'month' ? `m${minMonthOffset.value}` : `y${minYearOffset.value}`)

  if (activeTab.value === 'week') {
    minWeekOffset.value -= LOAD_MORE_STEPS.week
  } else if (activeTab.value === 'month') {
    minMonthOffset.value -= LOAD_MORE_STEPS.month
  } else {
    minYearOffset.value -= LOAD_MORE_STEPS.year
  }

  nextTick(() => {
    periodScrollIntoId.value = oldFirstId
    nextTick(() => {
      periodScrollIntoId.value = ''
      isLoadingMorePeriods.value = false
    })
  })
  // 扩展仅更新可见周期列表，不做预请求
}

// 仅在切换Tab时滚动到最新项一次，随后清空避免后续回弹
const scrollToLatest = (id) => {
  periodScrollIntoId.value = id
  nextTick(() => { periodScrollIntoId.value = '' })
}

// 计算当前tab/offset的日期范围（ISO: YYYY-MM-DD）
function getCurrentRange(expanded = false) {
  if (activeTab.value === 'week') {
    // 周：根据最左偏移或当前offset
    const off = expanded ? minWeekOffset.value : offset.value
    const base = dayjs().add(off, 'week')
    const start = base.startOf('week').format('YYYY-MM-DD')
    const end = (expanded ? dayjs() : base).endOf('week').format('YYYY-MM-DD')
    return { start, end }
  }
  if (activeTab.value === 'month') {
    const off = expanded ? minMonthOffset.value : offset.value
    const base = dayjs().add(off, 'month')
    const start = base.startOf('month').format('YYYY-MM-DD')
    const end = (expanded ? dayjs() : base).endOf('month').format('YYYY-MM-DD')
    return { start, end }
  }
  // year
  const off = expanded ? minYearOffset.value : offset.value
  const base = dayjs().add(off, 'year')
  const start = base.startOf('year').format('YYYY-MM-DD')
  const end = (expanded ? dayjs() : base).endOf('year').format('YYYY-MM-DD')
  return { start, end }
}

// ===== 导出：二次确认与流程（仅小程序） =====
// #ifdef MP-WEIXIN
const confirmExportCharts = () => {
  uni.showModal({
    title: '导出图片',
    content: '将保存到相册，是否继续？',
    success: (res) => { if (res.confirm) exportCharts() }
  })
}

const exportCharts = async () => {
  try {
    uni.showLoading({ title: '正在生成...' })
    const chartImgPath = await getChartTempImage()
    const imgInfo = await getImageInfo(chartImgPath)
    calcChartsCanvasHeight(imgInfo)
    // 等待一帧，确保尺寸生效
    setTimeout(async () => {
      await drawChartsToCanvas(chartImgPath, imgInfo)
      saveCanvasToAlbum('chartsExportCanvas', exportCanvasWidth * pixelRatio, exportCanvasHeight.value * pixelRatio)
    }, 50)
  } catch (e) {
    uni.hideLoading()
    uni.showToast({ title: '导出失败', icon: 'none' })
  }
}

const getChartTempImage = () => {
  return new Promise((resolve, reject) => {
    try {
      _chartImgResolve = resolve
      _chartImgReject = reject
      if (_chartImgTimer) clearTimeout(_chartImgTimer)
      _chartImgTimer = setTimeout(() => {
        _chartImgReject && _chartImgReject(new Error('chart timeout'))
      }, 3000)
      // 触发子组件导出
      if (lineChart.value && lineChart.value.getImage) {
        lineChart.value.getImage()
      } else {
        reject(new Error('chart ref not ready'))
      }
    } catch (e) {
      reject(e)
    }
  })
}

const onChartImage = (payload) => {
  if (_chartImgTimer) clearTimeout(_chartImgTimer)
  const path = payload && payload.base64 ? payload.base64 : (payload || '').tempFilePath || payload
  if (path && _chartImgResolve) _chartImgResolve(path)
  else if (_chartImgReject) _chartImgReject(new Error('no chart image'))
  _chartImgResolve = null
  _chartImgReject = null
}

const getImageInfo = (path) => {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({ src: path, success: resolve, fail: reject })
  })
}

const calcChartsCanvasHeight = (imgInfo) => {
  const titleH = 90
  // 按宽度等比缩放图表
  const chartH = Math.round((exportCanvasWidth / imgInfo.width) * imgInfo.height)
  const summaryH = 160
  const listHeaderH = 60
  const listItemH = 80
  const listLen = (boxTotals.value || []).length
  const listH = (listLen > 0 ? (listHeaderH + listLen * listItemH) : 100)
  const padding = 60
  exportCanvasHeight.value = titleH + chartH + summaryH + listH + padding
}

const drawChartsToCanvas = (chartImgPath, imgInfo) => {
  return new Promise((resolve) => {
    const ctx = uni.createCanvasContext('chartsExportCanvas')
    const w = exportCanvasWidth
    const h = exportCanvasHeight.value
    const primary = currentTheme.value === 'theme-dark' ? '#60A5FA' : '#2C5282'
    const textPrimary = '#333333'
    const textMuted = '#718096'

    // 背景
    ctx.setFillStyle('#FFFFFF')
    ctx.fillRect(0, 0, w, h)

    // 标题（具体时间范围）
    const label = (model.value && model.value.range && model.value.range.start)
      ? `${model.value.range.start} ~ ${model.value.range.end}`
      : (rangeLabel.value || '统计范围')
    ctx.setFillStyle(textPrimary)
    ctx.setFontSize(34)
    ctx.setTextAlign('center')
    ctx.fillText(label, w / 2, 56)

    // 绘制图表位图
    const chartDrawH = Math.round((w / imgInfo.width) * imgInfo.height)
    const chartY = 80
    ctx.drawImage(chartImgPath, 30, chartY, w - 60, chartDrawH)

    // 汇总信息
    const sumY = chartY + chartDrawH + 20
    const items = [
      { label: '卷烟', value: `${totals.value.cigarettes}`, unit: '根' },
      { label: '电子烟', value: `${totals.value.puffs}`, unit: '口' },
      { label: '盒数(卷烟)', value: `${boxCounts.value.cigarette || 0}`, unit: '盒' },
      { label: '盒数(电子)', value: `${boxCounts.value.electronic || 0}`, unit: '盒' }
    ]
    const col = 2
    const rowH = 70
    for (let i = 0; i < items.length; i++) {
      const row = Math.floor(i / col)
      const colIdx = i % col
      const cx = 30 + (w - 60) / col * colIdx + ((w - 60) / col) / 2
      const cy = sumY + 40 + row * rowH
      ctx.setFillStyle(textMuted)
      ctx.setFontSize(22)
      ctx.setTextAlign('center')
      ctx.fillText(items[i].label, cx, cy)
      ctx.setFillStyle(primary)
      ctx.setFontSize(34)
      ctx.fillText(`${items[i].value}${items[i].unit}`, cx, cy + 34)
    }

    // 盒子统计列表
    const headerY = sumY + rowH * 2 + 30
    ctx.setFillStyle(textPrimary)
    ctx.setFontSize(28)
    ctx.setTextAlign('left')
    ctx.fillText('按烟盒统计', 30, headerY)

    let listY = headerY + 20
    const list = boxTotals.value || []
    if (list.length === 0) {
      ctx.setFillStyle('#999999')
      ctx.setFontSize(24)
      ctx.setTextAlign('center')
      ctx.fillText('该范围内无记录', w / 2, listY + 40)
    } else {
      for (let i = 0; i < list.length; i++) {
        const item = list[i]
        const name = (item.box?.nickname || item.brand?.name || '未知') + ((item.virtual || item.brand?.virtual) ? '（云）' : '')
        const sub = item.box?.nickname && item.brand ? `(${item.brand.name})` : ''
        const value = item.type === 'cigarette' ? item.cigarettes : item.puffs
        const unit = item.type === 'cigarette' ? '根' : '口'

        // 名称
        ctx.setFillStyle(textPrimary)
        ctx.setFontSize(26)
        ctx.setTextAlign('left')
        ctx.fillText(name, 30, listY + 40)
        ctx.setFillStyle('#9CA3AF')
        ctx.setFontSize(22)
        if (sub) ctx.fillText(sub, 30 + ctx.measureText(name).width + 8, listY + 40)

        // 数值
        ctx.setFillStyle(primary)
        ctx.setFontSize(30)
        ctx.setTextAlign('right')
        ctx.fillText(`${value}${unit}`, w - 30, listY + 40)

        // 分隔线
        ctx.setStrokeStyle('#E5E7EB')
        ctx.setLineWidth(1)
        ctx.beginPath()
        ctx.moveTo(30, listY + 54)
        ctx.lineTo(w - 30, listY + 54)
        ctx.stroke()

        listY += 80
      }
    }

    ctx.draw(false, () => resolve())
  })
}

const saveCanvasToAlbum = (canvasId, destWidth, destHeight) => {
  try {
    uni.canvasToTempFilePath({
      canvasId,
      destWidth,
      destHeight,
      fileType: 'png',
      quality: 1,
      success: (res) => {
        const tempPath = res.tempFilePath
        uni.getSetting({
          success: (s) => {
            if (s.authSetting['scope.writePhotosAlbum']) {
              doSave(tempPath)
            } else {
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success: () => doSave(tempPath),
                fail: () => {
                  uni.hideLoading()
                  uni.showModal({
                    title: '需要授权',
                    content: '请在设置中允许保存到相册权限',
                    confirmText: '去设置',
                    success: (r) => { if (r.confirm) uni.openSetting() }
                  })
                }
              })
            }
          }
        })

        function doSave(path) {
          uni.saveImageToPhotosAlbum({
            filePath: path,
            success: () => {
              uni.hideLoading()
              uni.showToast({ title: '已保存到相册', icon: 'success' })
            },
            fail: () => {
              uni.hideLoading()
              uni.showToast({ title: '保存失败', icon: 'none' })
            }
          })
        }
      },
      fail: () => {
        uni.hideLoading()
        uni.showToast({ title: '生成失败', icon: 'none' })
      }
    })
  } catch (e) {
    uni.hideLoading()
    uni.showToast({ title: '导出异常', icon: 'none' })
  }
}
// #endif
</script>

<style lang="scss" scoped>
.charts-page {
  padding: $app-spacing-md;
  min-height: 100vh;
  background: var(--color-bg-page);
  // 预留底部空间，避免悬浮按钮遮挡
  padding-bottom: 160rpx;
}


.toolbar { margin-bottom: $app-spacing-sm; padding: $app-spacing-sm; }
.segmented {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  background: var(--color-bg-card);
  border-radius: 9999rpx;
  padding: 6rpx;
}
.seg-item {
  text-align: center;
  padding: 12rpx 0;
  color: var(--color-text-secondary);
  border-radius: 9999rpx;
  transition: all $app-transition-base ease;
}
.seg-item.active {
  background: var(--color-primary);
  color: var(--color-text-white);
}
.range-label { display: none; }

.period-bar { margin-bottom: $app-spacing-md; white-space: nowrap; }
.period-container { display: inline-flex; gap: $app-spacing-sm; padding: 0 $app-spacing-sm; }
.period-chip {
  padding: 12rpx 22rpx;
  border-radius: 9999rpx;
  background: var(--color-bg-section);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  transition: all $app-transition-base ease;
}
.period-chip.selected {
  background: var(--color-primary);
  color: var(--color-text-white);
  border-color: transparent;
}

.chart-section { padding: $app-spacing-sm; margin-bottom: $app-spacing-md; }
.chart-header { display: flex; justify-content: flex-end; margin-bottom: $app-spacing-sm; }
.chip { display: inline-flex; align-items: center; padding: 8rpx 16rpx; border-radius: 9999rpx; font-size: $app-font-size-sm; }
.chip--range { color: var(--color-text-secondary); border: 1px solid var(--color-border); background: transparent; }

.summary { margin-bottom: $app-spacing-md; padding: $app-spacing-sm; }
.summary-items { display: flex; justify-content: space-around; }
.summary-item { text-align: center; flex: 1; position: relative; }
.summary-item + .summary-item::before { content: ''; position: absolute; left: 0; top: 20%; bottom: 20%; width: 1px; background: var(--color-border); transform: scaleX(0.5); }
.summary-item .label { display: block; color: var(--color-text-muted); font-size: $app-font-size-sm; }
.summary-item .value { font-size: $app-font-size-xl; font-weight: 700; color: var(--color-text-primary); }
.summary-item .value.primary { color: var(--color-primary); }
.summary-item .unit { margin-left: 6rpx; color: var(--color-text-secondary); }

.boxes { padding: $app-spacing-sm; }
.boxes .section-title { font-weight: 700; color: var(--color-text-primary); }
.box-row { display: flex; justify-content: space-between; align-items: center; padding: $app-spacing-sm 0; border-bottom: 1px solid var(--color-border); }
.box-row:last-child { border-bottom: none; }
.box-title .brand { font-size: $app-font-size-base; color: var(--color-text-primary); }
.box-title .brand-sub { color: var(--color-text-muted); margin-left: 6rpx; }
.box-title .tag-cloud { margin-left: 8rpx; padding: 2rpx 8rpx; font-size: 20rpx; border-radius: 9999rpx; background: var(--color-bg-section); color: var(--color-secondary); border: 1px solid var(--color-border); }
.box-values .value { font-weight: 700; color: var(--color-primary); }
.box-values .unit { margin-left: 4rpx; color: var(--color-text-secondary); }

.empty { padding: $app-spacing-lg; text-align: center; }
.empty-text { color: var(--color-text-muted); }

// 导出按钮与隐藏画布（仅小程序）
// #ifdef MP-WEIXIN
.export-fab {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 99;
  background-color: var(--color-primary);
  color: var(--color-text-white);
  padding: 14rpx 28rpx;
  border-radius: 60rpx;
  box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.15);
}
.export-fab-text { font-weight: 700; font-size: $app-font-size-base; }
.export-canvas {
  position: fixed;
  left: -2000px;
  top: -2000px;
  z-index: -9999;
  background-color: #fff;
}
// #endif
</style>


