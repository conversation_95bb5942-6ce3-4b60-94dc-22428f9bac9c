<template>
  <view class="achievements-page" :class="currentTheme">
    <view class="summary card">
      <view class="sum-left">
        <text class="sum-title">成就</text>
        <text class="sum-sub">已解锁 {{ unlockedCount }} / {{ totalCount }}</text>
      </view>
      <view class="sum-right" v-if="unreadCount>0">
        <view class="dot"></view>
        <text class="unread-text">{{ unreadCount }}</text>
      </view>
    </view>

    <view class="seg">
      <view :class="['seg-item', tab==='positive'?'active':'']" @click="tab='positive'">正向</view>
      <view :class="['seg-item', tab==='negative'?'active':'']" @click="tab='negative'">反向</view>
      <view :class="['seg-item', tab==='milestone'?'active':'']" @click="tab='milestone'">里程碑</view>
    </view>

    <view class="grid">
      <view v-for="a in filteredCatalog" :key="a.id" class="badge-card" :class="badgeClass(a)">
        <view class="shield">
          <text class="shield-title">{{ a.name }}</text>
          <text class="shield-sub">{{ a.desc }}</text>
        </view>
        <view class="status">
          <template v-if="isUnlocked(a.id)">
            <text class="unlocked">已解锁</text>
            <text class="time">{{ unlockedTime(a.id) }}</text>
          </template>
          <template v-else>
            <view class="progress" v-if="progressOf(a)">
              <view class="bar"><view class="fill" :style="{ width: progressPercent(a) + '%' }" /></view>
              <text class="hint">{{ progressHint(a) }}</text>
            </view>
            <text class="locked" v-else>未解锁</text>
          </template>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useTobaccoStore } from '../../composables/useTobaccoStore.js'
import { useStatistics } from '../../composables/useStatistics.js'
import { useAchievements } from '../../composables/useAchievements.js'
import { useTheme } from '../../composables/useTheme.js'

const { consumptionRecords, activeBoxes, getBrand, initializeData } = useTobaccoStore()
const { overallStats } = useStatistics(consumptionRecords, activeBoxes, getBrand)
const { currentTheme } = useTheme()

const { catalog, achievementsState, evaluateAndSync, loadFromCloud, markAllRead } = useAchievements(consumptionRecords, activeBoxes, getBrand, overallStats)

const tab = ref('positive')

onMounted(async () => {
  await initializeData()
  await loadFromCloud()
  const r = await evaluateAndSync()
  if ((r.newlyAddedIds || []).length) {
    uni.showToast({ title: `解锁${r.newlyAddedIds.length}枚徽章`, icon: 'success' })
  }
  await markAllRead()
})

const totalCount = computed(() => catalog.length)
const unlockedCount = computed(() => Object.keys(achievementsState.value.unlockedMap || {}).length)
const unreadCount = computed(() => achievementsState.value.unreadCount || 0)
const filteredCatalog = computed(() => catalog.filter(c => c.group === tab.value))

const isUnlocked = (id) => !!(achievementsState.value.unlockedMap || {})[id]
const unlockedTime = (id) => {
  const a = (achievementsState.value.unlockedMap || {})[id]
  if (!a) return ''
  try { const d = new Date(a.unlockedAt); return `${d.getFullYear()}-${String(d.getMonth()+1).padStart(2,'0')}-${String(d.getDate()).padStart(2,'0')}` } catch(_) { return '' }
}
const progressOf = (a) => (achievementsState.value.progressMap || {})[a.id]
const progressPercent = (a) => {
  const p = progressOf(a); if (!p) return 0
  if (!p.target) return 0
  return Math.max(0, Math.min(100, Math.round((p.current / p.target) * 100)))
}
const progressHint = (a) => {
  const p = progressOf(a); if (!p) return ''
  return `${p.current || 0}/${p.target}`
}

const badgeClass = (a) => {
  const cls = [a.group]
  if (isUnlocked(a.id)) cls.push('unlocked')
  return cls.join(' ')
}
</script>

<style lang="scss" scoped>
.achievements-page {
  min-height: 100vh;
  padding: $app-spacing-md;
  background-color: var(--color-bg-page);
}

.summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $app-spacing-lg;
  margin-bottom: $app-spacing-lg;
}
.sum-left { display: flex; flex-direction: column; }
.sum-title { font-size: $app-font-size-xl; font-weight: 700; color: var(--color-text-primary); }
.sum-sub { font-size: $app-font-size-sm; color: var(--color-text-muted); }
.sum-right { display: flex; align-items: center; gap: 8rpx; }
.dot { width: 12rpx; height: 12rpx; background-color: #F59E0B; border-radius: 9999rpx; }
.unread-text { font-size: $app-font-size-sm; color: #F59E0B; }

.seg { display: grid; grid-template-columns: repeat(3,1fr); gap: $app-spacing-sm; margin-bottom: $app-spacing-md; }
.seg-item {
  text-align: center; padding: 18rpx 0; border-radius: $app-button-radius;
  background-color: var(--color-bg-card); color: var(--color-text-primary);
}
.seg-item.active { background-color: var(--color-primary); color: var(--color-text-white); }

.grid { display: grid; grid-template-columns: repeat(2,1fr); gap: $app-spacing-sm; }

.badge-card { padding: $app-spacing-md; border-radius: $app-card-radius; box-shadow: var(--shadow-sm); background-color: var(--color-bg-card); }
.badge-card.unlocked { box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.12); }

.shield { position: relative; padding: $app-spacing-md; border-radius: 12rpx; text-align: center; margin-bottom: $app-spacing-sm; }
.shield-title { font-weight: 700; font-size: $app-font-size-base; display: block; color: var(--color-text-primary); }
.shield-sub { font-size: $app-font-size-xs; color: var(--color-text-muted); }

.badge-card.positive .shield { border: 2rpx solid var(--color-primary); background: linear-gradient(180deg, rgba(44,82,130,0.06), transparent); }
.badge-card.negative .shield { border: 2rpx solid #8B0000; background: linear-gradient(180deg, rgba(139,0,0,0.08), transparent); }
.badge-card.milestone .shield { border: 2rpx solid #2C5282; background: linear-gradient(180deg, rgba(44,82,130,0.08), transparent); }

.status { display: flex; align-items: center; justify-content: space-between; }
.unlocked { color: var(--color-primary); font-weight: 600; }
.locked { color: var(--color-text-muted); }
.time { font-size: $app-font-size-xs; color: var(--color-text-muted); }

.progress { width: 100%; }
.bar { width: 100%; height: 12rpx; background-color: var(--color-bg-section); border-radius: 9999rpx; overflow: hidden; margin-bottom: 6rpx; }
.fill { height: 100%; background-color: var(--color-primary); }
.badge-card.negative .fill { background-color: #8B0000; }
.badge-card.milestone .fill { background-color: #2C5282; }
.hint { font-size: $app-font-size-xs; color: var(--color-text-secondary); }
</style>


