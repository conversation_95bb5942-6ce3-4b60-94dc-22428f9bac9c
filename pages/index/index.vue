<template>
  <view class="home-page" :class="currentTheme">
    <!-- 概览（精简三KPI） -->
    <view class="today-overview card" @click="goToStatistics">
      <view class="overview-kpis">
        <view class="kpi-item">
          <text class="kpi-label">花费总计</text>
          <text class="kpi-value primary">{{
            formatCurrency(userInfo.totalSpent || 0)
          }}</text>
        </view>
        <view class="kpi-item">
          <text class="kpi-label">总烟量</text>
          <text class="kpi-value"
            >{{ formatInteger(totalElectronicCartridgesUsed) }}颗 /
            {{ formatInteger(totalCompletedCigaretteBoxes) }}盒</text>
        </view>
        <view class="kpi-item">
          <text class="kpi-label">记录天数</text>
          <text class="kpi-value"
            >{{ formatInteger(userInfo.totalRecordDays || 0) }}天</text>
        </view>
      </view>
      <view class="overview-stats">
        <view class="stat-group">
          <text class="stat-label">今日已抽</text>
          <text class="stat-value"
            >{{ formatInteger(todayStats.totalCigarettes) }}根 /
            {{ formatInteger(todayStats.totalPuffs) }}口</text
          >
        </view>
        <view class="stat-group">
          <text class="stat-label">今日花费</text>
          <text class="stat-value primary">{{
            formatCurrency(todayStats.totalSpent)
          }}</text>
        </view>
        <view class="stat-group">
          <text class="stat-label">较昨日</text>
          <text class="stat-value" :class="trendClass">
            {{ trendPrefixCig }}{{ dailyComparison.cigaretteDiff }}根 /
            {{ trendPrefixPuff }}{{ dailyComparison.puffDiff }}口
          </text>
        </view>
      </view>
    </view>

    <!-- 活跃烟盒区域 -->
    <view class="active-boxes-section">
      <view class="section-header">
        <text class="section-title"
          >活跃烟盒 ({{ activeBoxesSorted.length }}/{{ maxActiveBoxes }})</text
        >
        <button v-if="activeBoxesSorted.length" class="btn-add" :disabled="!canAddBox" @click="addNewBox">
          <text class="iconfont">&#xe87a;</text>
        </button>
      </view>

      <scroll-view
        class="boxes-vertical-scroll"
        scroll-y="true"
        :scroll-into-view="scrollIntoViewId"
        :scroll-with-animation="true"
        v-if="activeBoxesSorted.length > 0"
      >
        <uni-swipe-action ref="swipeRef">
          <uni-swipe-action-item
            v-for="box in activeBoxesSorted"
            :key="box.id"
            :threshold="swipeThreshold"
            @change="onSwipeChange"
          >
            <template #left>
              <view
                class="swipe-btn swipe-btn--left"
                @click.stop="onSwipeSlotAction('完成', box)"
              >
                <text class="swipe-btn__text">完成</text>
              </view>
            </template>
            <view
              class="box-card"
              :id="'box-' + box.id"
              :class="{ active: selectedBoxId === box.id }"
              @click="selectBox(box)"
              @longpress.stop="openEditBoxModal(box)"
            >
              <view class="box-header">
                <text class="box-brand">{{ getBrandName(box.brandId) }}</text>
                <text class="box-nickname" v-if="box.nickname">{{
                  box.nickname
                }}</text>
              </view>
              <view class="box-progress">
                <view class="progress-bar">
                  <view
                    class="progress-fill"
                    :style="{ width: getProgressPercentage(box) + '%' }"
                  ></view>
                </view>
                <text class="progress-text">{{ getProgressText(box) }}</text>
              </view>
              <view class="box-footer">
                <text class="box-price">{{ formatCurrency(box.price) }}</text>
                <!-- <view class="box-actions">
                  <button class="btn-mini" @click.stop="quickAdd(box)">+{{ miniIncrementByBoxId[box.id] }}</button>
                </view> -->
              </view>
            </view>
            <template #right>
              <view
                class="swipe-btn swipe-btn--right"
                @click.stop="onSwipeSlotAction('删除', box)"
              >
                <text class="swipe-btn__text">删除</text>
              </view>
            </template>
          </uni-swipe-action-item>
        </uni-swipe-action>
      </scroll-view>

      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <text class="empty-text">还没有活跃的烟盒</text>
        <button class="btn-primary" @click="addNewBox">添加一个烟盒</button>
      </view>
    </view>

    <!-- 快速记录底部常驻条 -->
    <QuickRecordBar
      v-if="activeBoxesSorted.length > 0"
      ref="quickBarRef"
      :active-boxes="activeBoxesSorted"
      :selected-box-id="selectedBoxId"
      :haptics-enabled="hapticsEnabled"
      :get-brand-name="getBrandName"
      @update:selectedBoxId="
        (id) => {
          selectedBoxId = id;
        }
      "
      @record="handleQuickRecord"
      @go-virtual="goVirtualSmoking"
    />

    <!-- 编辑烟盒 弹窗 -->
    <CommonModal
      ref="editBoxPopup"
      position="center"
      :mask="true"
      :maskClosable="true"
      :animation="true"
      :zIndex="1000"
      :showClose="true"
      :showFooter="false"
      title="编辑烟盒"
    >
      <view class="edit-box-modal card">
        <view class="modal-form">
          <view class="form-row">
            <text class="form-label">昵称</text>
            <input
              class="form-input"
              v-model="editForm.nickname"
              placeholder="可选"
              maxlength="10"
            />
          </view>
          <view class="form-row">
            <text class="form-label">价格</text>
            <input
              class="form-input"
              type="digit"
              v-model.number="editForm.price"
              placeholder="请输入价格"
            />
            <text class="form-unit">元</text>
          </view>
          <view v-if="editForm.type === 'cigarette'">
            <view class="form-row">
              <text class="form-label">每盒总根数</text>
              <input
                class="form-input"
                type="number"
                v-model.number="editForm.cigaretteTotal"
                placeholder="如：20"
              />
              <text class="form-unit">根</text>
            </view>
          </view>
          <view v-else>
            <view class="form-row">
              <text class="form-label">每盒烟弹数</text>
              <input
                class="form-input"
                type="number"
                v-model.number="editForm.electronicTotalCartridges"
                placeholder="如：3"
              />
              <text class="form-unit">颗</text>
            </view>
            <view class="form-row">
              <text class="form-label">每颗口数</text>
              <input
                class="form-input"
                type="number"
                v-model.number="editForm.electronicPuffsPerCartridge"
                placeholder="如：300"
              />
              <text class="form-unit">口</text>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button
            class="btn-cancel"
            @click="
              () => editBoxPopup && editBoxPopup.close && editBoxPopup.close()
            "
          >
            取消
          </button>
          <button class="btn-confirm btn-primary" @click="submitEditBox">
            保存
          </button>
        </view>
      </view>
    </CommonModal>
  </view>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { useTobaccoStore } from "../../composables/useTobaccoStore.js";
import { useUserSettings } from "../../composables/useUserSettings.js";
import { useStatistics } from "../../composables/useStatistics.js";
import { formatCurrency, formatInteger } from "../../utils/formatters.js";
import { useTheme } from "../../composables/useTheme.js";
import dayjs from "../../utils/dayjs-setup.js";
import QuickRecordBar from "../../components/QuickRecordBar.vue";
import CommonModal from "../../components/CommonModal.vue";

// 状态管理
const tobaccoStore = useTobaccoStore();
const settingsStore = useUserSettings();

const {
  activeBoxes,
  consumptionRecords,
  activeBoxesSorted,
  initializeData,
  addConsumptionRecord,
  completeTobaccoBox,
  completeCurrentCartridge,
  removeTobaccoBox,
  updateTobaccoBox,
  getBoxById,
  getBrand,
  undoConsumptionRecord,
  fetchRecords,
  setConsumptionRecords,
  loadRecentDays,
} = tobaccoStore;

const {
  maxActiveBoxes,
  canAddNewBox,
  initializeUserData,
  userInfo,
  userSettings: userSettingsState,
  setQuickRecordPresets,
  setHapticsEnabled,
  checkDailyLimit,
} = settingsStore;

const { todayStats, dailyComparison, overallStats } = useStatistics(
  consumptionRecords,
  activeBoxes,
  getBrand
);

// 主题
const { currentTheme } = useTheme();
// 趋势样式与前缀
const trendClass = computed(() =>
  dailyComparison.value.cigaretteDiff > 0 || dailyComparison.value.puffDiff > 0
    ? "danger"
    : "success"
);
const trendPrefixCig = computed(() =>
  dailyComparison.value.cigaretteDiff > 0 ? "+" : ""
);
const trendPrefixPuff = computed(() =>
  dailyComparison.value.puffDiff > 0 ? "+" : ""
);

// 本地状态
const selectedBoxId = ref("");
const selectedBoxIndex = ref(0);
const recordAmount = ref(1);
const lastRecordId = ref("");
const quickBarRef = ref(null);
const scrollIntoViewId = ref("");

// 计算属性
const selectedBox = computed(() => {
  return (
    activeBoxesSorted.value.find((box) => box.id === selectedBoxId.value) ||
    activeBoxesSorted.value[0]
  );
});

const boxOptions = computed(() => {
  return activeBoxesSorted.value.map((box) => ({
    value: box.id,
    label: getBoxDisplayName(box),
  }));
});

const canAddBox = computed(() => {
  return canAddNewBox(activeBoxesSorted.value.length);
});

const quickPresets = computed(
  () =>
    userSettingsState.value.quickRecordPresets || {
      cigarette: [1, 2, 3],
      electronic: [5, 10, 20],
    }
);
const hapticsEnabled = computed(
  () => userSettingsState.value.hapticsEnabled !== false
);

// 计算每个box的微增量（取对应类型预设的首位）
const miniIncrementByBoxId = computed(() => {
  const map = {};
  activeBoxesSorted.value.forEach((box) => {
    const inc =
      box.type === "electronic"
        ? quickPresets.value.electronic?.[0] || 5
        : quickPresets.value.cigarette?.[0] || 1;
    map[box.id] = inc;
  });
  return map;
});

// 总烟量（完整消费）：电子烟颗数 + 卷烟盒数
const totalElectronicCartridgesUsed = computed(() => {
  try {
    let sum = 0;
    (activeBoxes.value || []).forEach((box) => {
      if (!box || box.type !== "electronic" || !box.specs) return;
      const e = box.specs.electronic || {};
      if (box.isCompleted) {
        const totalCartridges = Number(e.totalCartridges) || 0;
        sum += Math.max(0, totalCartridges);
      } else {
        const currentCartridge = Number(e.currentCartridge || 1);
        sum += Math.max(0, currentCartridge - 1);
      }
    });
    return sum;
  } catch (_) {
    return 0;
  }
});

const totalCompletedCigaretteBoxes = computed(() => {
  try {
    return (activeBoxes.value || []).filter(
      (b) => b && b.type === "cigarette" && b.isCompleted
    ).length;
  } catch (_) {
    return 0;
  }
});

// 降低触发门槛，适配纵向 scroll 内的横向滑动
const swipeThreshold = 14;

// 编辑弹窗
const editBoxPopup = ref(null);
const editingBoxId = ref("");
const editForm = ref({
  type: "cigarette",
  nickname: "",
  price: 0,
  cigaretteTotal: 20,
  electronicTotalCartridges: 3,
  electronicPuffsPerCartridge: 300,
});

// 页面初始化
onShow(async () => {
  try {
    uni.showLoading({ title: "加载中", mask: true });
    await initializeUserData();
    await initializeData();
    // 拉取最近8天记录，保障今日/昨日/较昨日统计
    await loadRecentDays(8)
    // 默认选择第一个烟盒
    if (activeBoxesSorted.value.length > 0) {
      selectedBoxId.value = activeBoxesSorted.value[0].id;
    }
  } finally {
    try {
      uni.hideLoading();
    } catch (e) {}
  }
});

// 方法定义
const getBrandName = (brandId) => {
  const brand = getBrand(brandId);
  return brand ? brand.name : "未知品牌";
};

const getBoxDisplayName = (box) => {
  const brandName = getBrandName(box.brandId);
  return box.nickname ? `${box.nickname} (${brandName})` : brandName;
};

const getProgressPercentage = (box) => {
  if (box.type === "cigarette") {
    return (box.specs.cigarette.consumed / box.specs.cigarette.total) * 100;
  } else {
    const currentCartridgeProgress =
      (box.specs.electronic.currentCartridgePuffs /
        box.specs.electronic.cartridgePuffsLimit) *
      100;
    const completedCartridges = box.specs.electronic.currentCartridge - 1;
    const totalProgress =
      (completedCartridges / box.specs.electronic.totalCartridges) * 100 +
      currentCartridgeProgress / box.specs.electronic.totalCartridges;
    return Math.min(totalProgress, 100);
  }
};

const getProgressText = (box) => {
  if (box.type === "cigarette") {
    return `${box.specs.cigarette.consumed}/${box.specs.cigarette.total}`;
  } else {
    return `第${box.specs.electronic.currentCartridge}颗 ${box.specs.electronic.currentCartridgePuffs}/${box.specs.electronic.cartridgePuffsLimit}`;
  }
};

// 计算盒子剩余额度（不允许超过）
const getRemainingCapacity = (box) => {
  if (!box || !box.specs) return 0;
  if (box.type === "cigarette") {
    const total = box.specs?.cigarette?.total || 0;
    const consumed = box.specs?.cigarette?.consumed || 0;
    return Math.max(0, total - consumed);
  } else {
    const e = box.specs?.electronic || {};
    const limit = e.cartridgePuffsLimit || 0;
    const totalPuffs = (e.totalCartridges || 0) * limit;
    const usedPuffs = Math.max(
      0,
      ((e.currentCartridge || 1) - 1) * limit + (e.currentCartridgePuffs || 0)
    );
    return Math.max(0, totalPuffs - usedPuffs);
  }
};

// 约束记录数量并判断是否将达上限
const clampRecordAmountForBox = (box, requested) => {
  if (!box) return { amount: 0, remaining: 0, willComplete: false };
  // 卷烟仍按盒容量裁剪；电子烟放开裁剪，不触发自动完成盒逻辑
  if (box.type === "cigarette") {
    const remaining = getRemainingCapacity(box);
    const amount = Math.min(requested || 0, remaining);
    const willComplete = remaining > 0 && amount === remaining;
    return { amount, remaining, willComplete };
  }
  // electronic: 允许超过每颗/整盒上限，由后端推进进度
  const amount = Number(requested) || 0;
  return { amount, remaining: Number.POSITIVE_INFINITY, willComplete: false };
};

const selectBox = (box) => {
  selectedBoxId.value = box.id;
  selectedBoxIndex.value = activeBoxesSorted.value.findIndex(
    (b) => b.id === box.id
  );
};

const onBoxChange = (e) => {
  selectedBoxIndex.value = e.detail.value;
  selectedBoxId.value = boxOptions.value[e.detail.value].value;
};

const increaseAmount = () => {
  recordAmount.value += 1;
};

const decreaseAmount = () => {
  if (recordAmount.value > 1) {
    recordAmount.value -= 1;
  }
};

const onAmountBlur = () => {
  if (!recordAmount.value || recordAmount.value < 1) {
    recordAmount.value = 1;
  }
};

const recordConsumption = async () => {
  const box = selectedBox.value;
  if (!box) return;
  // 近1小时内有同类型云吸则二次确认
  const within = await interceptIfRecentVirtual(box.type);
  if (within === "cancel" || within === "jump") return;
  const { amount, remaining, willComplete } = clampRecordAmountForBox(
    box,
    recordAmount.value
  );
  if (!(amount > 0)) {
    // 已满则尝试自动完成
    if (remaining === 0 && !box.isCompleted) {
      await completeTobaccoBox(box.id);
      uni.showToast({ title: "该烟盒已达上限，已自动完成", icon: "none" });
    } else {
      uni.showToast({ title: "数量无效", icon: "none" });
    }
    return;
  }
  const allowed = await ensureWithinDailyLimitOrConfirm(amount, box.type);
  if (!allowed) return;
  const recordData = { boxId: box.id, amount, type: box.type, note: "" };
  await addConsumptionRecord(recordData);
  if (willComplete) {
    await completeTobaccoBox(box.id);
    uni.showToast({ title: "记录成功，已完成该烟盒", icon: "success" });
  } else {
    uni.showToast({ title: "记录成功", icon: "success" });
  }
  recordAmount.value = 1;
};

const completeBox = (box) => {
  completeTobaccoBox(box.id);
  uni.showToast({ title: "已完成", icon: "success" });
};

const addNewBox = () => {
  if (!canAddBox.value) {
    uni.showToast({
      title: `最多只能有${maxActiveBoxes.value}个活跃烟盒`,
      icon: "none",
    });
    return;
  }

  uni.navigateTo({
    url: "/pages/add-box/add-box",
  });
};

// 概览跳转
const goToStatistics = () => {
  uni.navigateTo({ url: "/pages/record-consumption/record" });
};

// 云吸入口
const goVirtualSmoking = () => {
  uni.navigateTo({ url: "/pages/virtual-smoking/virtual" });
};

// 卡片微+1增量
const quickAdd = async (box) => {
  if (!box) return;
  const inc = miniIncrementByBoxId.value[box.id] || 1;
  const { amount, remaining, willComplete } = clampRecordAmountForBox(box, inc);
  if (!(amount > 0)) {
    if (remaining === 0 && !box.isCompleted) {
      await completeTobaccoBox(box.id);
      uni.showToast({ title: "该烟盒已达上限，已自动完成", icon: "none" });
    }
    return;
  }
  const allowed = await ensureWithinDailyLimitOrConfirm(amount, box.type);
  if (!allowed) return;
  const payload = { boxId: box.id, amount, type: box.type };
  const record = await addConsumptionRecord(payload);
  lastRecordId.value = record.id;
  if (willComplete) {
    await completeTobaccoBox(box.id);
    uni.showToast({ title: "这盒烟抽光了", icon: "success" });
  }
  if (quickBarRef.value && quickBarRef.value.showUndo) {
    const unit = box.type === "electronic" ? "口" : "根";
    quickBarRef.value.showUndo(record.id, amount, unit);
  }
};

// 处理底部条记录与撤销
const handleQuickRecord = async (evt) => {
  if (evt?.undoRecordId) {
    await undoConsumptionRecord(evt.undoRecordId);
    uni.showToast({ title: "已撤销", icon: "none" });
    return;
  }
  const box = activeBoxesSorted.value.find((b) => b.id === evt.boxId);
  if (!box) return;
  const within = await interceptIfRecentVirtual(evt.type);
  if (within === "cancel" || within === "jump") return;
  const { amount, remaining, willComplete } = clampRecordAmountForBox(
    box,
    evt.amount
  );
  if (!(amount > 0)) {
    if (remaining === 0 && !box.isCompleted) {
      await completeTobaccoBox(box.id);
      uni.showToast({ title: "该烟盒已达上限，已自动完成", icon: "none" });
    }
    return;
  }
  const allowed = await ensureWithinDailyLimitOrConfirm(amount, evt.type);
  if (!allowed) return;
  const record = await addConsumptionRecord({
    boxId: evt.boxId,
    amount,
    type: evt.type,
  });
  lastRecordId.value = record.id;
  if (willComplete) {
    await completeTobaccoBox(box.id);
    uni.showToast({ title: "这盒烟抽光了", icon: "success" });
  }
  const unit = evt.type === "electronic" ? "口" : "根";
  if (quickBarRef.value && quickBarRef.value.showUndo) {
    quickBarRef.value.showUndo(record.id, amount, unit);
  }
};

// 触感反馈
const vibrate = () => {
  if (hapticsEnabled.value && uni.vibrateShort) {
    try {
      uni.vibrateShort();
    } catch (e) {}
  }
};

// 更强烈的震动反馈（用于超限提示）
const vibrateStrong = () => {
  try {
    if (hapticsEnabled.value) {
      if (uni.vibrateLong) {
        try {
          uni.vibrateLong();
        } catch (e) {}
      } else if (uni.vibrateShort) {
        try {
          uni.vibrateShort();
        } catch (e) {}
        setTimeout(() => {
          try {
            uni.vibrateShort();
          } catch (e) {}
        }, 150);
      }
    }
  } catch (_) {}
};

// 超限检查：超限则强震动并二次确认，允许继续
const ensureWithinDailyLimitOrConfirm = async (amount, type) => {
  try {
    if (!(amount > 0)) return true;
    const currentCig = Number(todayStats.value?.totalCigarettes || 0);
    const currentPuffs = Number(todayStats.value?.totalPuffs || 0);
    const proposedCig = type === "cigarette" ? currentCig + amount : currentCig;
    const proposedPuffs =
      type === "electronic" ? currentPuffs + amount : currentPuffs;
    const result = checkDailyLimit(proposedCig, proposedPuffs);
    if (!result || !result.exceeded) return true;
    vibrateStrong();
    return await new Promise((resolve) => {
      uni.showModal({
        title: "超过每日限制",
        content:
          result.type === "cigarettes"
            ? `今日卷烟：${result.current}/${result.limit} 根。是否继续记录？`
            : `今日电子烟：${result.current}/${result.limit} 口。是否继续记录？`,
        confirmText: "继续",
        success: (res) => {
          resolve(!!res.confirm);
        },
        fail: () => resolve(false),
      });
    });
  } catch (_) {
    return true;
  }
};

// 拦截：近1小时内刚云吸
const { getLastFakeTimestamp } = tobaccoStore;
const interceptIfRecentVirtual = async (type) => {
  try {
    const last = Number(getLastFakeTimestamp(type) || 0);
    if (!last) return "ok";
    const ONE_HOUR = 60 * 60 * 1000;
    if (Date.now() - last < ONE_HOUR) {
      return await new Promise((resolve) => {
        uni.showModal({
          title: "要不先缓一缓？",
          content:
            "刚刚你用轻清烟盒稳住了这口气，建议至少间隔1小时再记录真实吸烟。现在要怎么做？",
          confirmText: "仍要记录",
          cancelText: "去云吸",
          success: (res) => {
            if (res.cancel) {
              goVirtualSmoking();
              resolve("jump");
            } else resolve("ok");
          },
          fail: () => resolve("cancel"),
        });
      });
    }
    return "ok";
  } catch (_) {
    return "ok";
  }
};

// 滑动项插槽点击（完成/删除）
const swipeRef = ref(null);
const onSwipeSlotAction = async (action, box) => {
  if (!box || !action) return;
  if (action === "完成") {
    vibrate();
    if (box.type === "electronic") {
      const r = await completeCurrentCartridge(box.id);
      if (r && r.ok) {
        if (r.boxCompleted) {
          uni.showToast({
            title: "已完成当前烟弹并完成该烟盒",
            icon: "success",
          });
          ensureSelectedAfterListChange(box.id);
        } else {
          uni.showToast({ title: "已完成当前烟弹", icon: "success" });
        }
      } else {
        uni.showToast({ title: "操作失败", icon: "none" });
      }
    } else {
      await completeTobaccoBox(box.id);
      uni.showToast({ title: "已完成", icon: "success" });
      ensureSelectedAfterListChange(box.id);
    }
  } else if (action === "删除") {
    vibrate();
    confirmRemoveBox(box);
  }
  // 主动收起所有已展开项（容错调用）
  try {
    swipeRef.value && swipeRef.value.closeAll && swipeRef.value.closeAll();
  } catch (e) {}
};

const onSwipeChange = () => {};

const confirmRemoveBox = (box) => {
  if (!box) return;
  uni.showModal({
    title: "删除烟盒",
    content: "该操作不可恢复，确定删除该烟盒？",
    confirmText: "删除",
    success: async (res) => {
      if (res.confirm) {
        const ok = await removeTobaccoBox(box.id);
        if (ok) {
          uni.showToast({ title: "删除成功", icon: "success" });
          ensureSelectedAfterListChange(box.id);
        } else {
          uni.showToast({ title: "删除失败", icon: "none" });
        }
      }
    },
  });
};

const ensureSelectedAfterListChange = (removedId) => {
  if (selectedBoxId.value === removedId) {
    const first = activeBoxesSorted.value[0];
    selectedBoxId.value = first ? first.id : "";
  }
};

// 选中烟盒变化时，滚动到对应卡片
watch(
  () => selectedBoxId.value,
  (id) => {
    scrollIntoViewId.value = id ? `box-${id}` : "";
  }
);

// 打开编辑弹窗
const openEditBoxModal = (box) => {
  if (!box) return;
  vibrate();
  editingBoxId.value = box.id;
  editForm.value.type = box.type;
  editForm.value.nickname = box.nickname || "";
  editForm.value.price = box.price || 0;
  if (box.type === "cigarette") {
    editForm.value.cigaretteTotal = box.specs?.cigarette?.total || 20;
  } else {
    const e = box.specs?.electronic || {};
    editForm.value.electronicTotalCartridges = e.totalCartridges || 3;
    editForm.value.electronicPuffsPerCartridge = e.cartridgePuffsLimit || 300;
  }
  if (editBoxPopup.value && editBoxPopup.value.open) {
    editBoxPopup.value.open();
  }
};

const submitEditBox = async () => {
  const id = editingBoxId.value;
  if (!id) return;
  // 获取当前盒子最新数据（先从活跃列表，找不到则从全量状态）
  const target =
    activeBoxesSorted.value.find((b) => b.id === id) || getBoxById(id);
  if (!target) return;

  // 校验与组装
  const payload = {
    nickname: editForm.value.nickname,
    price: editForm.value.price,
  };
  if (target.type === "cigarette") {
    const consumed = target.specs?.cigarette?.consumed || 0;
    const newTotal = Number(editForm.value.cigaretteTotal) || 0;
    if (!(newTotal > 0) || newTotal < consumed) {
      uni.showToast({ title: "总根数不能小于已消费数量", icon: "none" });
      return;
    }
    payload.specs = {
      cigarette: {
        ...target.specs.cigarette,
        total: newTotal,
      },
    };
  } else {
    const e = target.specs?.electronic || {};
    const newCartridges = Number(editForm.value.electronicTotalCartridges) || 0;
    const newPuffs = Number(editForm.value.electronicPuffsPerCartridge) || 0;
    if (!(newCartridges > 0) || newCartridges < (e.currentCartridge || 1)) {
      uni.showToast({
        title: "烟弹总数不能小于当前已用烟弹序号",
        icon: "none",
      });
      return;
    }
    if (!(newPuffs > 0) || newPuffs < (e.currentCartridgePuffs || 0)) {
      uni.showToast({ title: "每颗口数不能小于当前已抽口数", icon: "none" });
      return;
    }
    payload.specs = {
      electronic: {
        ...e,
        totalCartridges: newCartridges,
        cartridgePuffsLimit: newPuffs,
      },
    };
  }

  const updated = await updateTobaccoBox(id, payload);
  if (updated) {
    uni.showToast({ title: "保存成功", icon: "success" });
    if (editBoxPopup.value && editBoxPopup.value.close)
      editBoxPopup.value.close();
  } else {
    uni.showToast({ title: "保存失败", icon: "none" });
  }
};

// 首页不再提供功能快捷入口，聚焦快速记录
</script>

<style lang="scss" scoped>
.home-page {
  padding: 0 $app-spacing-md;
  box-sizing: border-box;
  min-height: 100vh;
  background-color: var(--color-bg-page);
  /* add bottom padding to avoid QuickRecordBar overlap */
  padding-bottom: calc(env(safe-area-inset-bottom) + 80rpx);
  border: 1px solid transparent;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.cloud-entry {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 360rpx;
  margin: 0 auto;
  width: fit-content;
  background: var(--color-bg-card);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  border-radius: 9999rpx;
  padding: 10rpx 20rpx;
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 12rpx;
  z-index: 999;
}
.breath-badge {
  color: #45c07a;
  font-weight: 600;
}
.cloud-entry-text {
  font-weight: 600;
}

.today-overview {
  padding: $app-spacing-sm;
  margin-bottom: $app-spacing-lg;
  margin-top: $app-spacing-md;
  flex: 0 0 auto;
}

.overview-header {
  margin-bottom: $app-spacing-md;
}

.overview-title {
  font-size: $app-font-size-xl;
  font-weight: bold;
  color: var(--color-text-primary);
}

.overview-stats {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.overview-kpis {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: $app-spacing-sm;
}

.kpi-item {
  text-align: center;
  min-width: 160rpx;
}

.kpi-label {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
  display: block;
  margin-bottom: 8rpx;
}

.kpi-value {
  font-size: $app-font-size-base;
  font-weight: 600;
  color: var(--color-text-primary);
}

.stat-group {
  text-align: center;
  min-width: 120rpx;
}

.stat-label {
  font-size: $app-font-size-xs;
  color: var(--color-text-muted);
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: $app-font-size-base;
  font-weight: 600;
  color: var(--color-text-primary);

  &.primary {
    color: var(--color-primary);
  }

  &.success {
    color: var(--color-accent);
  }

  &.danger {
    color: var(--color-danger);
  }
}

.active-boxes-section {
  margin-bottom: $app-spacing-lg;
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.boxes-vertical-scroll {
  // max-height 在内联样式绑定
  width: 100%;
  /* rely on page bottom padding instead of fixed calc height */
  max-height: none;
  flex: 1 1 auto;
  min-height: 0;
  height: 100%;
  /* ensure last card not covered by QuickRecordBar */
  padding-bottom: calc(env(safe-area-inset-bottom));
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $app-spacing-md;
  flex: 0 0 auto;
}

.section-title {
  font-size: $app-font-size-lg;
  font-weight: bold;
  color: var(--color-text-primary);
}

.btn-add {
  background-color: var(--color-primary);
  color: var(--color-text-white);
  border: none;
  border-radius: $app-button-radius;
  padding: $app-spacing-xs $app-spacing-md;
  font-size: $app-font-size-sm;
  margin: 0;
  &:disabled {
    background-color: var(--color-text-muted);
  }
}

.boxes-container {
  display: flex;
  flex-direction: column;
  gap: $app-spacing-md;
}

.box-card {
  background-color: var(--color-bg-card);
  padding: $app-spacing-md;
  box-shadow: var(--shadow-sm);
  min-width: 400rpx;
  border: 4rpx solid transparent;
  /* spacing between cards */
  margin-bottom: $app-spacing-md;

  &.active {
    border-color: var(--color-primary);
  }

  &:active {
    background-color: var(--color-bg-section);
  }
}

.box-header {
  margin-bottom: $app-spacing-sm;
}

.box-brand {
  font-size: $app-font-size-lg;
  font-weight: bold;
  color: var(--color-text-primary);
  display: block;
}

.box-nickname {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
  display: block;
}

.box-progress {
  margin-bottom: $app-spacing-md;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: var(--color-border);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: $app-spacing-xs;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  transition: width $app-transition-base;
}

.progress-text {
  font-size: $app-font-size-sm;
  color: var(--color-text-secondary);
}

.box-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-price {
  font-size: $app-font-size-lg;
  font-weight: bold;
  color: var(--color-secondary);
}

.box-actions {
  display: flex;
  align-items: center;
  gap: $app-spacing-sm;
}

.btn-mini {
  background-color: var(--color-border);
  color: var(--color-text-primary);
  border: none;
  border-radius: $app-button-radius;
  padding: $app-spacing-xs $app-spacing-sm;
  font-size: $app-font-size-sm;
  margin: 0;
}

.btn-complete {
  background-color: var(--color-accent);
  color: var(--color-text-white);
  border: none;
  border-radius: $app-button-radius;
  padding: $app-spacing-xs $app-spacing-sm;
  font-size: $app-font-size-sm;
  margin: 0;
}

.empty-state {
  text-align: center;
  padding: $app-spacing-xl;
}

.empty-text {
  font-size: $app-font-size-lg;
  color: var(--color-text-muted);
  margin-bottom: $app-spacing-lg;
  display: block;
}

.quick-record-section {
  padding: $app-spacing-lg;
}

.record-header {
  margin-bottom: $app-spacing-md;
}

.record-title {
  font-size: $app-font-size-lg;
  font-weight: bold;
  color: var(--color-text-primary);
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: $app-spacing-md;
}

.form-label {
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
  margin-right: $app-spacing-sm;
  min-width: 160rpx;
}

.picker-text {
  flex: 1;
  padding: $app-spacing-sm;
  background-color: var(--color-bg-section);
  border-radius: $app-input-radius;
  color: var(--color-text-primary);
}

.amount-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.btn-amount {
  background-color: var(--color-border);
  color: var(--color-text-primary);
  border: none;
  border-radius: $app-button-radius;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-value {
  flex: 1;
  text-align: center;
  margin: 0 $app-spacing-sm;
  padding: $app-spacing-xs;
  border: 1px solid var(--color-border);
  border-radius: $app-input-radius;
  color: var(--color-text-primary);
}

.amount-unit {
  margin-left: $app-spacing-sm;
  color: var(--color-text-muted);
}

.btn-record {
  width: 100%;
  padding: $app-spacing-md;
  font-size: $app-font-size-lg;

  &:disabled {
    background-color: var(--color-text-muted);
  }
}

/* 首页移除快捷入口区样式 */
/* 编辑弹窗样式优化（作用域限定在 .edit-box-modal 内） */
.edit-box-modal {
  display: flex;
  flex-direction: column;
  max-width: 92vw;
}

.edit-box-modal .modal-form {
  padding: $app-spacing-sm $app-spacing-sm 0 $app-spacing-sm;
}

.edit-box-modal .form-row {
  display: flex;
  align-items: center;
  gap: $app-spacing-sm;
  margin-bottom: $app-spacing-md;
}

.edit-box-modal .form-label {
  min-width: 180rpx;
  color: var(--color-text-primary);
}

.edit-box-modal .form-input {
  flex: 1;
  height: 72rpx;
  padding: 0 $app-spacing-sm;
  border: 1px solid var(--color-border);
  border-radius: $app-input-radius;
  background-color: var(--color-bg-section);
  color: var(--color-text-primary);
}
.edit-box-modal .form-input::placeholder {
  color: var(--color-text-muted);
}

.edit-box-modal .form-unit {
  color: var(--color-text-muted);
  min-width: 64rpx;
  text-align: right;
}

.edit-box-modal .modal-footer {
  position: sticky;
  bottom: 0;
  display: flex;
  gap: $app-spacing-sm;
  padding: $app-spacing-sm $app-spacing-md $app-spacing-md $app-spacing-md;
  background-color: var(--color-bg-card);
  border-top: 1px solid var(--color-border);
}

.edit-box-modal .btn-cancel,
.edit-box-modal .btn-confirm {
  flex: 1;
  height: 80rpx;
  border-radius: $app-button-radius;
  margin: 0;
  /* 统一按钮文字样式与内边距，确保上下左右居中 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: $app-font-size-base;
  line-height: 1;
  box-sizing: border-box;
}

.edit-box-modal .btn-cancel {
  background-color: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.edit-box-modal .btn-confirm.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-white);
}

/* 自定义插槽按钮样式 */
.swipe-btn {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160rpx;
  padding: 0 28rpx;
}

.swipe-btn--left {
  background-color: var(--color-accent);
  border-top-left-radius: $app-card-radius;
  border-bottom-left-radius: $app-card-radius;
}

.swipe-btn--right {
  background-color: var(--color-danger);
  border-top-right-radius: $app-card-radius;
  border-bottom-right-radius: $app-card-radius;
}

.swipe-btn__text {
  color: var(--color-text-white);
  font-weight: 600;
  letter-spacing: 1rpx;
}
</style>
