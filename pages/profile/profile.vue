<template>
  <view class="profile-page" :class="currentTheme">
    <!-- 用户信息区域（保持原样） -->
    <view class="user-info-section" @click="handleUserInfoClick">
      <view class="user-avatar">
        <image :src="displayAvatar" class="avatar-image" mode="aspectFill"></image>
      </view>
      <view class="user-details">
        <text class="user-nickname">{{ displayNickname }}</text>
        <view v-if="!isLoggedIn" class="user-hint">
          <text>未登录，首次需完善资料；已注册点击确认即可登录</text>
        </view>
      </view>
      <view v-if="isLoggedIn" class="user-edit-indicator">
        <view class="edit-pencil-placeholder"></view>
        <text class="edit-text">编辑</text>
        <text class="iconfont edit-arrow">&#xe87f;</text>
      </view>
    </view>
    <!-- 统计详情（替换原快速功能区位置，包含用户信息） -->
    <view class="quick-stats card">
      <view class="qs-row">
        <view class="qs-item">
          <text class="qs-title">卷烟总计</text>
          <text class="qs-value">{{ overallStats.totalCigarettes }} 根</text>
        </view>
        <view class="qs-item">
          <text class="qs-title">电子烟总计</text>
          <text class="qs-value">{{ overallStats.totalPuffs }} 口</text>
        </view>
        <view class="qs-item">
          <text class="qs-title">总花费</text>
          <text class="qs-value">{{ formatCurrency(overallStats.totalSpent) }}</text>
        </view>
        <view class="qs-item">
          <text class="qs-title">总天数</text>
          <text class="qs-value">{{ overallStats.totalDays }}</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="navigateToCollection">
        <view class="iconfont menu-icon">&#xeb9d;</view>
        <view class="menu-content">
          <view class="menu-head">
            <text class="menu-title">我抽过的烟</text>
            <view class="iconfont menu-arrow">&#xe883;</view>
          </view>
          <text class="menu-subtitle">查看收藏的品牌</text>
        </view>
      </view>
      
      <view class="menu-item" @click="navigateToStatistics">
        <view class="iconfont menu-icon">&#xeb99;</view>
        <view class="menu-content">
          <view class="menu-head">
            <text class="menu-title">数据统计</text>
            <view class="iconfont menu-arrow">&#xe883;</view>
          </view>
          <text class="menu-subtitle">查看详细数据</text>
        </view>
      </view>

      <view class="menu-item" @click="navigateToAchievements">
        <view class="iconfont menu-icon">&#xeba6;</view>
        <view class="menu-content">
          <view class="menu-head">
            <text class="menu-title">解锁成就</text>
            <view class="iconfont menu-arrow">&#xe883;</view>
          </view>
          <text class="menu-subtitle">坚持记录，达成徽章</text>
        </view>
        <view v-if="unreadAchievements>0" class="red-dot"></view>
      </view>

      

      <view class="menu-item" @click="navigateToSettings">
        <view class="iconfont menu-icon">&#xe871;</view>
        <view class="menu-content">
          <view class="menu-head">
            <text class="menu-title">设置</text>
            <view class="iconfont menu-arrow">&#xe883;</view>
          </view>
          <text class="menu-subtitle">个性化配置</text>
        </view>
      </view>

      <view class="menu-item" @click="navigateToHelp">
        <view class="iconfont menu-icon">&#xe872;</view>
        <view class="menu-content">
          <view class="menu-head">
            <text class="menu-title">帮助中心</text>
            <view class="iconfont menu-arrow">&#xe883;</view>
          </view>
          <text class="menu-subtitle">使用指南与客服</text>
        </view>
      </view>

      <view class="menu-item" @click="showAbout">
        <view class="iconfont menu-icon">&#xeba5;</view>
        <view class="menu-content">
          <view class="menu-head">
            <text class="menu-title">关于应用</text>
            <view class="iconfont menu-arrow">&#xe883;</view>
          </view>
          <text class="menu-subtitle">版本信息</text>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">轻清戒烟日记 v1.0.0</text>
    </view>

    <!-- 用户资料编辑弹窗（整合至本页） -->
    <ProfileEditor
      :show="showProfileEditor"
      :userData="userInfo"
      @close="closeProfileEditor"
      @save="saveProfile"
    />
  </view>
</template>

<script setup>
import { onMounted, computed, ref, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useUserSettings } from '../../composables/useUserSettings.js'
import { useTobaccoStore } from '../../composables/useTobaccoStore.js'
import { useStatistics } from '../../composables/useStatistics.js'
import { useAchievements } from '../../composables/useAchievements.js'
import { useTheme } from '../../composables/useTheme.js'
import { formatCurrency } from '../../utils/formatters.js'
import ProfileEditor from '../../components/ProfileEditor.vue'
import loginApi from '../../utils/login.js'

// 状态管理
const { userInfo, initializeUserData, isLoggedIn, login, updateUserProfile } = useUserSettings()
const { consumptionRecords, activeBoxes, getBrand, initializeData } = useTobaccoStore()
const { overallStats } = useStatistics(consumptionRecords, activeBoxes, getBrand)
const { achievementsState, evaluateAndSync, loadFromCloud } = useAchievements(consumptionRecords, activeBoxes, getBrand, overallStats)

// 主题
const { currentTheme } = useTheme()

const defaultAvatar = 'https://mp-4c4f3180-781d-4096-9c5d-db2fc2dfa05e.cdn.bspapp.com/help/default-avatar.jpg'
const resolvedAvatar = ref(defaultAvatar)

const resolveAvatar = async (val) => {
  try {
    if (!val) { resolvedAvatar.value = defaultAvatar; return }
    const isUrl = /^(https?:)?\/\//i.test(String(val))
    if (isUrl) { resolvedAvatar.value = val; return }
    const res = await uniCloud.getTempFileURL({ fileList: [val] })
    const list = res?.fileList || res?.data || []
    const first = list[0]
    resolvedAvatar.value = first?.tempFileURL || defaultAvatar
  } catch (e) { resolvedAvatar.value = defaultAvatar }
}

watch(() => userInfo.value?.avatar, (val) => { resolveAvatar(val) }, { immediate: true })

const displayAvatar = computed(() => resolvedAvatar.value)
const displayNickname = computed(() => userInfo.value?.nickname || '未设置昵称')

const showProfileEditor = ref(false)

// 页面初始化
onMounted(() => {
  initializeUserData()
  initializeData()
})

// 点击用户信息区域逻辑
const handleUserInfoClick = async () => {
  // 已登录：直接打开资料编辑
  if (isLoggedIn.value) {
    showProfileEditor.value = true
    return
  }
  // 未登录：先判断是否已注册
  const check = await loginApi.checkUserExists()
  if (check.success && check.exists) {
    // 已注册：弹二次确认
    uni.showModal({
      title: '登录确认',
      content: '检测到您已注册，是否直接登录？',
      confirmText: '登录',
      success: async (res) => {
        if (res.confirm) {
          const result = await login()
          if (result.success) {
            uni.showToast({ title: '登录成功', icon: 'success' })
          } else {
            uni.showToast({ title: result.message || '登录失败', icon: 'none' })
          }
        }
      }
    })
  } else {
    // 未注册或检查失败：打开资料编辑（填写后点保存即完成注册+登录）
    showProfileEditor.value = true
  }
}

const closeProfileEditor = () => { showProfileEditor.value = false }

// 保存资料：未登录视为首次注册（先本地保存，再登录；登录成功后将刚填写的资料再同步到云端）
const saveProfile = async (profileData) => {
  try {
    // 先更新（登录状态：云端；未登录：本地）
    const result = await updateUserProfile(profileData)
    if (!result.success) {
      uni.showToast({ title: result.message || '更新失败', icon: 'none' })
      return
    }
    // 若未登录：登录创建账号
    if (!isLoggedIn.value) {
      const loginRes = await login()
      if (!loginRes.success) {
        uni.showToast({ title: loginRes.message || '登录失败', icon: 'none' })
        return
      }
      // 登录成功后将刚填写的资料再次推送到云端
      await updateUserProfile(profileData)
    }
    uni.showToast({ title: '资料更新成功', icon: 'success' })
    closeProfileEditor()
  } catch (e) {
    uni.showToast({ title: '更新失败', icon: 'none' })
  }
}

// 导航方法
const navigateToCollection = () => { uni.navigateTo({ url: '/pages/tobacco-collection/collection' }) }
const navigateToSettings = () => {
  if (!isLoggedIn.value) {
    uni.showToast({ title: '请先登录', icon: 'none' })
    return
  }
  uni.navigateTo({ url: '/pages/settings/settings' })
}
const navigateToStatistics = () => { uni.navigateTo({ url: '/pages/statistics/statistics' }) }
const navigateToAchievements = () => { uni.navigateTo({ url: '/pages/achievements/achievements' }) }
const navigateToHelp = () => { uni.navigateTo({ url: '/pages/help/help' }) }

const showAbout = () => {
  uni.showModal({
    title: '关于轻清戒烟日记',
    content: '这是一个帮助记录和管理抽烟习惯的日记小程序。\n\n版本：v1.1.3\n开发者：轻清戒烟日记团队',
    showCancel: false
  })
}

// 红点：未读成就数量
const unreadAchievements = computed(() => achievementsState.value.unreadCount || 0)

// 进入我的页时尝试刷新云端未读
onMounted(async () => {
  try { await loadFromCloud(); await evaluateAndSync() } catch (_) {}
})

// tabBar 页面每次显示时刷新未读
onShow(async () => {
  try { await loadFromCloud(); await evaluateAndSync() } catch (_) {}
})
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: var(--color-bg-page);
  padding: $app-spacing-md;
}

.user-info-section {
  background-color: var(--color-bg-card);
  border-radius: $app-card-radius;
  padding: $app-spacing-sm;
  margin-bottom: $app-spacing-lg;
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
}

.quick-stats {
  padding: $app-spacing-lg;
  background-color: var(--color-bg-card);
  border-radius: $app-card-radius;
  box-shadow: var(--shadow-sm);
  margin-bottom: $app-spacing-lg;
}

/* 移除了 quick-stats 内的用户信息样式 */

.qs-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $app-spacing-md;
}

@media (max-width: 360px) {
  .qs-row { grid-template-columns: repeat(2, 1fr); }
}

.qs-item { text-align: center; }
.qs-title { font-size: $app-font-size-xs; color: var(--color-text-muted); display: block; margin-bottom: $app-spacing-xs; }
.qs-value { font-size: $app-font-size-base; font-weight: 600; color: var(--color-primary); }

.user-avatar {
  margin-right: $app-spacing-sm;
}

.avatar-image {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background-color: var(--color-border);
}

.user-details {
  flex: 1;
}

.user-nickname {
  font-size: $app-font-size-base;
  font-weight: bold;
  color: var(--color-text-primary);
  margin-bottom: $app-spacing-xs;
}

.user-hint {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
}

.user-edit-indicator {
  display: flex;
  align-items: center;
  margin-left: $app-spacing-sm;
  color: var(--color-text-muted);
  font-size: $app-font-size-sm;
}

.edit-pencil-placeholder {
  width: 32rpx;
  height: 32rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}

.edit-text {
  margin-right: 8rpx;
  color: var(--color-text-muted);
}

.edit-arrow {
  font-size: $app-font-size-base;
  color: var(--color-text-muted);
}

.user-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: $app-spacing-lg;
}

.stat-value {
  font-size: $app-font-size-lg;
  font-weight: bold;
  color: $app-primary-color;
}

.stat-label {
  font-size: $app-font-size-sm;
  color: $app-text-muted;
}

.stat-divider {
  width: 1px; /* hairline keep px */
  height: 60rpx;
  background-color: $app-border-light;
  margin-right: $app-spacing-lg;
}

.menu-section {
  background-color: var(--color-bg-card);
  border-radius: $app-card-radius;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: $app-spacing-sm; /* 更紧凑 */
  border-bottom: 1px solid var(--color-border);
  min-height: 96rpx; /* 更小的行高目标 */
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: var(--color-bg-section);
  }
}

.menu-item { position: relative; }
.red-dot { position: absolute; left: 40rpx;top:24rpx; width: 12rpx; height: 12rpx; background-color: #F59E0B; border-radius: 9999rpx; }

.menu-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0; /* 使省略号生效 */
}

.menu-head {
  display: flex;
  align-items: center; /* 箭头与主标题垂直居中 */
}

.iconfont.menu-icon, .menu-icon {
  font-size: 40rpx; /* 缩小图标 */
  margin-right: $app-spacing-sm;
  width: 56rpx;
  height: 40rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  color: var(--color-primary);
}

.menu-title {
  font-size: $app-font-size-base; /* 缩小主标题 */
  color: var(--color-text-primary);
  font-weight: 500;
  line-height: 40rpx;
  margin-bottom: 4rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-subtitle {
  font-size: $app-font-size-xs; /* 缩小副标题 */
  color: var(--color-text-muted);
  line-height: 32rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.iconfont.menu-arrow, .menu-arrow {
  margin-left: auto;
  font-size: $app-font-size-base; /* 缩小箭头字号 */
  color: var(--color-text-muted);
  line-height: 1;
}

/* 交互态：按压时图标使用主题按压色 */
.menu-item:active .menu-icon { color: var(--color-primary-press); }

.version-info {
  text-align: center;
  margin-top: $app-spacing-xl;
}

.version-text {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
}
</style>
