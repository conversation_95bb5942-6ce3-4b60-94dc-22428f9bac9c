<template>
  <view class="calendar-page" :class="currentTheme">
    <!-- 日历组件 -->
    <view class="calendar-section card">
      <uni-calendar 
        ref="calendar"
        :insert="true"
        :range="true"
        :selected="calendarData"
        @change="onCalendarChange"
        @monthSwitch="onMonthSwitch"
      ></uni-calendar>
    </view>

    <!-- 选中日期信息 -->
    <view class="date-info-section card" v-if="selectedDate">
      <view class="date-header">
        <text class="date-title">{{ formatSelectedDate() }}</text>
        <view class="date-right">
          <text class="date-weekday">{{ getWeekday(selectedDate) }}</text>
          <view
            v-if="selectedDateLimitStatus"
            class="limit-status"
            :class="selectedDateLimitStatus.exceeded ? 'exceeded' : 'ok'"
          >{{ selectedDateLimitStatus.exceeded ? '超限' : '未超限' }}</view>
        </view>
      </view>
      
      <view class="date-stats" v-if="selectedDateStats">
        <view class="stat-row">
          <view class="stat-item">
            <text class="stat-label">卷烟</text>
            <text class="stat-value">{{ selectedDateStats.totalCigarettes }}根</text>
          </view>
          <view class="stat-item" v-if="selectedDateStats.totalPuffs > 0">
            <text class="stat-label">电子烟</text>
            <text class="stat-value">{{ selectedDateStats.totalPuffs }}口</text>
          </view>
          <view class="stat-item" v-if="todayVirtual.totalCigarettes>0 || todayVirtual.totalPuffs>0">
            <text class="stat-label">云吸</text>
            <text class="stat-value">{{ todayVirtual.totalCigarettes }}根 / {{ todayVirtual.totalPuffs }}口</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">记录次数</text>
            <text class="stat-value">{{ selectedDateStats.recordCount }}次</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 区间统计 -->
    <view class="date-info-section card" v-if="selectedRange.start && selectedRange.end && selectedRangeStats">
      <view class="date-header">
        <text class="date-title">{{ selectedRange.start }} ~ {{ selectedRange.end }}</text>
      </view>
      <view class="stat-row">
        <view class="stat-item">
          <text class="stat-label">卷烟合计</text>
          <text class="stat-value">{{ selectedRangeStats.totalCigarettes }}根</text>
        </view>
        <view class="stat-item" v-if="selectedRangeStats.totalPuffs > 0">
          <text class="stat-label">电子烟合计</text>
          <text class="stat-value">{{ selectedRangeStats.totalPuffs }}口</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">总花费</text>
          <text class="stat-value">{{ formatCurrency(selectedRangeStats.totalSpent) }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">记录次数</text>
          <text class="stat-value">{{ selectedRangeStats.recordCount }}次</text>
        </view>
      </view>
    </view>

    <!-- 详细记录列表 -->
    <view class="records-section" v-if="selectedDate && selectedDateStats">
      <view class="section-header">
        <text class="section-title">详细记录</text>
      </view>
      
      <view class="records-list" v-if="selectedDateStats.records.length > 0">
        <view 
          class="record-item card" 
          v-for="record in selectedDateStats.records" 
          :key="record.id"
        >
          <view class="record-header">
            <text class="record-time">{{ record.time }}</text>
            <view class="record-type" :class="record.type">
              {{ record.type === 'cigarette' ? '卷烟' : '电子烟' }}
            </view>
          </view>
          
          <view class="record-content">
            <view class="record-brand">
              <text class="brand-name">{{ record.box ? (record.brand ? record.brand.name : '未知品牌') : '轻清烟盒' }}</text>
              <text class="box-nickname" v-if="record.box && record.box.nickname">
                ({{ record.box.nickname }})
              </text>
            </view>
            
            <view class="record-amount">
              <text class="amount-text">
                {{ record.amount }}{{ record.type === 'cigarette' ? '根' : '口' }}
              </text>
            </view>
          </view>
          <view class="record-badge" v-if="record.fake"><text class="breath">[呼吸]</text></view>
          
          <view class="record-note" v-if="record.note">
            <text class="note-text">{{ record.note }}</text>
          </view>
        </view>
      </view>
      
      <view class="empty-records" v-else>
        <text class="empty-text">这一天没有记录</text>
      </view>
    </view>

    <!-- 月度统计 -->
    <view class="monthly-stats-section card" v-if="showMonthlyStats">
      <view class="stats-header">
        <text class="stats-title">{{ currentMonthText }}统计</text>
      </view>
      
      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-number">{{ displayMonthlyStats.totalCigarettes }}</text>
          <text class="stat-name">卷烟 (根)</text>
        </view>
        <view class="stat-card" v-if="displayMonthlyStats.totalPuffs > 0">
          <text class="stat-number">{{ displayMonthlyStats.totalPuffs }}</text>
          <text class="stat-name">电子烟 (口)</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ formatCurrency(displayMonthlyStats.totalSpent) }}</text>
          <text class="stat-name">花费</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ displayMonthlyStats.recordCount }}</text>
          <text class="stat-name">记录次数</text>
        </view>
      </view>
      
      <view class="daily-average">
        <text class="average-text">
           日均: {{ formatInteger(displayMonthlyStats.dailyAverage.cigarettes) }}根
          <span v-if="displayMonthlyStats.dailyAverage.puffs > 0">
            , {{ formatInteger(displayMonthlyStats.dailyAverage.puffs) }}口
          </span>
        </text>
      </view>
    </view>
    <!-- 悬浮导出按钮（微信小程序） -->
    <!-- #ifdef MP-WEIXIN -->
    <view class="export-fab" @click="confirmExportStats">
      <text class="export-fab-text">导出</text>
    </view>
    <!-- 隐藏导出用的Canvas -->
    <canvas
      canvas-id="statsExportCanvas"
      id="statsExportCanvas"
      class="export-canvas"
      :style="{ width: exportCanvasWidth + 'px', height: exportCanvasHeight + 'px' }"
    ></canvas>
    <!-- #endif -->
  </view>
  </template>

  <script setup>
  import { ref, computed, onMounted, getCurrentInstance, nextTick } from 'vue'
  import { useTobaccoStore } from '../../composables/useTobaccoStore.js'
  import { useStatistics } from '../../composables/useStatistics.js'
  import dayjs from '../../utils/dayjs-setup.js'
  import { getToday } from '../../utils/dateUtils.js'
  import { useTheme } from '../../composables/useTheme.js'
  import { formatCurrency, formatInteger } from '../../utils/formatters.js'
  import { useUserSettings } from '../../composables/useUserSettings.js'
  
  // 状态管理
  const { activeBoxes, getBrand, initializeData, fetchRecords } = useTobaccoStore()
  const pageRecords = ref([])
  const rangeRecords = ref([]) // 跨月区间临时数据，不覆盖页面月份数据
  const statsMonth = useStatistics(pageRecords, activeBoxes, getBrand)
  const statsRange = useStatistics(rangeRecords, activeBoxes, getBrand)
  const { todayVirtual } = statsMonth
  
  // 主题
  const { currentTheme } = useTheme()
  const { isDailyLimitEnabled, dailyLimitSettings, checkDailyLimit } = useUserSettings()
  
  // 本地状态
  const selectedDate = ref('')
  const currentMonth = ref('')
  const currentYear = ref('')
  const selectedRange = ref({ start: '', end: '' })
  
  // 导出画布设置（仅小程序）
  // #ifdef MP-WEIXIN
  const exportCanvasWidth = 750
  const exportCanvasHeight = ref(0)
  const pixelRatio = uni.getSystemInfoSync().pixelRatio || 1
  const instance = getCurrentInstance()
  // #endif
  
  // 页面初始化
  onMounted(async () => {
    await initializeData()
    // 默认选择今天
    selectedDate.value = getToday()
    const today = dayjs()
    currentYear.value = today.year()
    currentMonth.value = today.month() + 1
    // 只请求当前显示月份的数据
    try {
      const startISO = dayjs(`${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-01`, 'YYYY-MM-DD').startOf('month').format('YYYY-MM-DD')
      const endISO = dayjs(startISO, 'YYYY-MM-DD').endOf('month').format('YYYY-MM-DD')
      pageRecords.value = await fetchRecords(startISO, endISO)
    } catch (_) {}
  })
  
  // 计算属性
  const selectedDateStats = computed(() => {
    if (!selectedDate.value) return null
    return statsMonth.getDateStats(selectedDate.value)
  })
  
  const selectedRangeStats = computed(() => {
    const start = selectedRange.value.start
    const end = selectedRange.value.end
    if (!(start && end)) return null
    // 若区间完全落在当前显示月份，基于月份数据计算；否则基于 rangeRecords 计算
    const curYM = `${currentYear.value}-${String(currentMonth.value).padStart(2,'0')}`
    const startYM = String(start).slice(0,7)
    const endYM = String(end).slice(0,7)
    if (startYM === curYM && endYM === curYM) {
      return statsMonth.getRangeStats(start, end)
    }
    return statsRange.getRangeStats(start, end)
  })
  
  const themePrimaryColor = computed(() => currentTheme.value === 'theme-dark' ? '#60A5FA' : '#2C5282')
  const okGreen = '#48BB78'
  const deepRed = '#8B0000'

  const calendarData = computed(() => {
    // 获取当前月份的日历数据
    const startDate = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-01`
    const endDateStr = dayjs(`${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-01`, 'YYYY-MM-DD')
      .endOf('month')
      .format('YYYY-MM-DD')
    
    const heatmapData = statsMonth.getCalendarHeatmap(startDate, endDateStr)
    
    // 转换为uni-calendar需要的格式
    // 仅为有记录的日期生成打点数据
    return heatmapData
      .filter(item => item.records > 0)
      .map(item => {
        let color = themePrimaryColor.value
        if (isDailyLimitEnabled.value) {
          const res = checkDailyLimit(item.cigarettes, item.puffs)
          color = res.exceeded ? deepRed : okGreen
        }
        return {
          date: item.date,
          info: `${item.cigarettes}/${item.puffs}`,
          color,
          data: {
            cigarettes: item.cigarettes,
            puffs: item.puffs,
            records: item.records
          }
        }
      })
  })
  
  // 基于当前显示的年月计算月度统计
  const displayMonthlyStats = computed(() => {
    if (!currentYear.value || !currentMonth.value) {
      return { totalCigarettes: 0, totalPuffs: 0, totalSpent: 0, recordCount: 0, dailyAverage: { cigarettes: 0, puffs: 0 } }
    }
    const startDate = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-01`
    const endDate = dayjs(startDate, 'YYYY-MM-DD').endOf('month').format('YYYY-MM-DD')
    const stats = statsMonth.getRangeStats(startDate, endDate)
    const days = stats.days || dayjs(endDate, 'YYYY-MM-DD').date()
    return {
      ...stats,
      dailyAverage: {
        cigarettes: days ? Math.round((stats.totalCigarettes / days) * 10) / 10 : 0,
        puffs: days ? Math.round((stats.totalPuffs / days) * 10) / 10 : 0
      }
    }
  })

  const currentMonthText = computed(() => {
    return `${currentYear.value}年${currentMonth.value}月`
  })
  
  const showMonthlyStats = computed(() => {
    if (selectedDate.value) return true
    if (selectedRange.value.start && selectedRange.value.end) {
      const start = dayjs(selectedRange.value.start, 'YYYY-MM-DD')
      const end = dayjs(selectedRange.value.end, 'YYYY-MM-DD')
      if (start.isValid() && end.isValid()) {
        return start.isSame(end, 'month')
      }
      return false
    }
    return true
  })
  
  // 方法定义
  const onCalendarChange = async (e) => {
    const before = e.range && e.range.before
    const after = e.range && e.range.after
    if (before && after) {
      const sameDay = dayjs(before, 'YYYY-MM-DD').isSame(dayjs(after, 'YYYY-MM-DD'), 'day')
      if (sameDay) {
        selectedRange.value = { start: '', end: '' }
        selectedDate.value = before
        const d = dayjs(before, 'YYYY-MM-DD')
        currentYear.value = d.year()
        currentMonth.value = d.month() + 1
      } else {
        selectedDate.value = ''
        selectedRange.value = { start: before, end: after }
        const sameMonth = dayjs(before, 'YYYY-MM-DD').isSame(dayjs(after, 'YYYY-MM-DD'), 'month')
        if (sameMonth) {
          const d = dayjs(before, 'YYYY-MM-DD')
          currentYear.value = d.year()
          currentMonth.value = d.month() + 1
        }
        // 区间仅在跨月时单独请求到 rangeRecords；同月内不请求，复用当前月份数据
        try {
          const curYM = `${currentYear.value}-${String(currentMonth.value).padStart(2,'0')}`
          const startYM = String(before).slice(0,7)
          const endYM = String(after).slice(0,7)
          if (startYM !== curYM || endYM !== curYM) {
            rangeRecords.value = await fetchRecords(before, after)
          } else {
            rangeRecords.value = []
          }
        } catch (_) {}
      }
    } else {
      selectedRange.value = { start: '', end: '' }
      selectedDate.value = e.fulldate
      currentYear.value = e.year
      currentMonth.value = e.month
      // 仅加载所选日所在月份（用于热力/单日）
      try {
        const monthStart = dayjs(`${e.year}-${String(e.month).padStart(2, '0')}-01`, 'YYYY-MM-DD').startOf('month').format('YYYY-MM-DD')
        const monthEnd = dayjs(monthStart, 'YYYY-MM-DD').endOf('month').format('YYYY-MM-DD')
        pageRecords.value = await fetchRecords(monthStart, monthEnd)
      } catch (_) {}
    }
  }
  
  const onMonthSwitch = (event) => {
    currentYear.value = event.year
    currentMonth.value = event.month
    // 切换月时仅加载对应整月
    ;(async () => {
      try {
        const startISO = dayjs(`${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-01`, 'YYYY-MM-DD').startOf('month').format('YYYY-MM-DD')
        const endISO = dayjs(startISO, 'YYYY-MM-DD').endOf('month').format('YYYY-MM-DD')
        pageRecords.value = await fetchRecords(startISO, endISO)
      } catch (_) {}
    })()
  }
  
  const formatSelectedDate = () => {
    if (!selectedDate.value) return ''
    const parts = String(selectedDate.value).split(/[-\/]/)
    if (parts.length !== 3) return ''
    const month = Number(parts[1])
    const day = Number(parts[2])
    return `${month}月${day}日`
  }
  
  const getWeekday = (dateStr) => {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const d = dateStr.includes('/')
      ? dayjs(dateStr, 'YYYY/MM/DD')
      : dayjs(dateStr, 'YYYY-MM-DD')
    return weekdays[d.day()]
  }

  const selectedDateLimitStatus = computed(() => {
    if (!selectedDate.value || !selectedDateStats.value) return null
    if (!isDailyLimitEnabled.value) return null
    return checkDailyLimit(
      selectedDateStats.value.totalCigarettes,
      selectedDateStats.value.totalPuffs
    )
  })
  
  // ===== 导出：二次确认与流程（仅小程序） =====
  // #ifdef MP-WEIXIN
  const confirmExportStats = () => {
    uni.showModal({
      title: '导出图片',
      content: '将保存到相册，是否继续？',
      success: (res) => {
        if (res.confirm) {
          exportStats()
        }
      }
    })
  }

  const exportStats = async () => {
    try {
      uni.showLoading({ title: '正在生成...' })
      calcStatsCanvasHeight()
      await nextTick()
      // 留一小帧等待样式应用
      setTimeout(async () => {
        await drawStatsToCanvas()
        saveCanvasToAlbum('statsExportCanvas', exportCanvasWidth * pixelRatio, exportCanvasHeight.value * pixelRatio)
      }, 50)
    } catch (e) {
      uni.hideLoading()
      uni.showToast({ title: '导出失败', icon: 'none' })
    }
  }

  const calcStatsCanvasHeight = () => {
    // 标题区 + 主统计卡片 + 内边距
    const titleH = 100
    // 单日/区间卡片高度略有差异，取相近固定高度
    const cardH = selectedDate.value ? 220 : 260
    const padding = 60
    exportCanvasHeight.value = titleH + cardH + padding
  }

  const drawStatsToCanvas = () => {
    return new Promise((resolve) => {
      const ctx = uni.createCanvasContext('statsExportCanvas', instance && instance.proxy)
      const w = exportCanvasWidth
      const h = exportCanvasHeight.value
      const pr = pixelRatio

      // 背景
      ctx.setFillStyle('#FFFFFF')
      ctx.fillRect(0, 0, w, h)

      // 标题（日期或区间）
      const primary = currentTheme.value === 'theme-dark' ? '#60A5FA' : '#2C5282'
      const textPrimary = '#333333'
      const textMuted = '#718096'

      let title = ''
      if (selectedDate.value) {
        title = `${formatSelectedDate()} ${getWeekday(selectedDate.value)}`
      } else if (selectedRange.value.start && selectedRange.value.end) {
        title = `${selectedRange.value.start} ~ ${selectedRange.value.end}`
      } else {
        title = '统计'
      }

      ctx.setFillStyle(textPrimary)
      ctx.setFontSize(36)
      ctx.setTextAlign('center')
      ctx.fillText(title, w / 2, 60)

      // 主统计卡片
      const cardX = 30
      const cardY = 100
      const cardW = w - 60
      const cardH = selectedDate.value ? 180 : 220

      // 卡片背景
      ctx.setFillStyle('#F8FAFC')
      ctx.fillRect(cardX, cardY, cardW, cardH)

      // 分割与文字
      ctx.setFillStyle(textMuted)
      ctx.setFontSize(26)

      if (selectedDate.value && selectedDateStats.value) {
        // 卷烟/电子烟/记录次数
        const items = [
          { label: '卷烟', value: `${selectedDateStats.value.totalCigarettes}根` },
          ...(selectedDateStats.value.totalPuffs > 0 ? [{ label: '电子烟', value: `${selectedDateStats.value.totalPuffs}口` }] : []),
          { label: '记录次数', value: `${selectedDateStats.value.recordCount}次` }
        ]
        const col = items.length
        for (let i = 0; i < items.length; i++) {
          const cx = cardX + (cardW / col) * i + (cardW / col) / 2
          ctx.setFillStyle(textMuted)
          ctx.setFontSize(24)
          ctx.setTextAlign('center')
          ctx.fillText(items[i].label, cx, cardY + 60)
          ctx.setFillStyle(primary)
          ctx.setFontSize(40)
          ctx.fillText(items[i].value, cx, cardY + 120)
        }
      } else if (selectedRange.value.start && selectedRange.value.end && selectedRangeStats.value) {
        const items = [
          { label: '卷烟合计', value: `${selectedRangeStats.value.totalCigarettes}根` },
          ...(selectedRangeStats.value.totalPuffs > 0 ? [{ label: '电子烟合计', value: `${selectedRangeStats.value.totalPuffs}口` }] : []),
          { label: '总花费', value: `${formatCurrency(selectedRangeStats.value.totalSpent)}` },
          { label: '记录次数', value: `${selectedRangeStats.value.recordCount}次` }
        ]
        const col = 2
        const rowH = 80
        for (let i = 0; i < items.length; i++) {
          const row = Math.floor(i / col)
          const colIdx = i % col
          const cx = cardX + (cardW / col) * colIdx + (cardW / col) / 2
          const cy = cardY + 50 + row * rowH
          ctx.setFillStyle(textMuted)
          ctx.setFontSize(24)
          ctx.setTextAlign('center')
          ctx.fillText(items[i].label, cx, cy)
          ctx.setFillStyle(primary)
          ctx.setFontSize(34)
          ctx.fillText(items[i].value, cx, cy + 38)
        }
      } else {
        ctx.setFillStyle('#999999')
        ctx.setFontSize(26)
        ctx.setTextAlign('center')
        ctx.fillText('暂无可导出的统计内容', w / 2, cardY + 100)
      }

      ctx.draw(false, () => resolve())
    })
  }

  const saveCanvasToAlbum = (canvasId, destWidth, destHeight) => {
    try {
      uni.canvasToTempFilePath({
        canvasId,
        destWidth,
        destHeight,
        fileType: 'png',
        quality: 1,
        success: (res) => {
          const tempPath = res.tempFilePath
          uni.getSetting({
            success: (s) => {
              if (s.authSetting['scope.writePhotosAlbum']) {
                doSave(tempPath)
              } else {
                uni.authorize({
                  scope: 'scope.writePhotosAlbum',
                  success: () => doSave(tempPath),
                  fail: () => {
                    uni.hideLoading()
                    uni.showModal({
                      title: '需要授权',
                      content: '请在设置中允许保存到相册权限',
                      confirmText: '去设置',
                      success: (r) => { if (r.confirm) uni.openSetting() }
                    })
                  }
                })
              }
            }
          })

          function doSave(path) {
            uni.saveImageToPhotosAlbum({
              filePath: path,
              success: () => {
                uni.hideLoading()
                uni.showToast({ title: '已保存到相册', icon: 'success' })
              },
              fail: () => {
                uni.hideLoading()
                uni.showToast({ title: '保存失败', icon: 'none' })
              }
            })
          }
        },
        fail: () => {
          uni.hideLoading()
          uni.showToast({ title: '生成失败', icon: 'none' })
        }
      })
    } catch (e) {
      uni.hideLoading()
      uni.showToast({ title: '导出异常', icon: 'none' })
    }
  }
  // #endif
  </script>
  
  <style lang="scss" scoped>
  .calendar-page {
    padding: $app-spacing-md;
    min-height: 100vh;
    background-color: var(--color-bg-page);
    // 预留底部空间，避免悬浮按钮遮挡
    padding-bottom: 160rpx;
  }
  
  .calendar-section {
    margin-bottom: $app-spacing-lg;
    overflow: hidden;
  }
  
  .date-info-section {
    padding: $app-spacing-lg;
    margin-bottom: $app-spacing-lg;
  }
  
  .date-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $app-spacing-md;
  }

  .date-right {
    display: flex;
    align-items: center;
    gap: $app-spacing-sm;
  }
  
  .date-title {
    font-size: $app-font-size-xl;
    font-weight: bold;
    color: var(--color-text-primary);
  }
  
  .date-weekday {
    font-size: $app-font-size-base;
    color: var(--color-text-muted);
  }

  .limit-status {
    padding: 2rpx 12rpx;
    border-radius: 9999rpx;
    font-size: $app-font-size-xs;
    color: #fff;
  }
  .limit-status.ok {
    background-color: #48BB78;
  }
  .limit-status.exceeded {
    background-color: #8B0000;
  }
  
  .stat-row {
    display: flex;
    justify-content: space-around;
  }
  
  .stat-item {
    text-align: center;
  }
  
  .stat-label {
    font-size: $app-font-size-sm;
    color: var(--color-text-muted);
    display: block;
    margin-bottom: $app-spacing-xs;
  }
  
  .stat-value {
    font-size: $app-font-size-lg;
    font-weight: bold;
    color: var(--color-primary);
  }
  
  .records-section {
    margin-bottom: $app-spacing-lg;
  }
  
  .section-header {
    margin-bottom: $app-spacing-md;
  }
  
  .section-title {
    font-size: $app-font-size-lg;
    font-weight: bold;
    color: var(--color-text-primary);
  }
  
  .record-item {
    padding: $app-spacing-md;
    margin-bottom: $app-spacing-sm;
  }
  
  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $app-spacing-sm;
  }
  
  .record-time {
    font-size: $app-font-size-lg;
    font-weight: bold;
    color: var(--color-text-primary);
  }
  
  .record-type {
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
    font-size: $app-font-size-xs;
    color: var(--color-text-white);
    
    &.cigarette {
      background-color: var(--color-secondary);
    }
    
    &.electronic {
      background-color: var(--color-accent);
    }
  }
  
  .record-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $app-spacing-sm;
  }
  
  .brand-name {
    font-size: $app-font-size-base;
    color: var(--color-text-primary);
    font-weight: 500;
  }
  
  .box-nickname {
    font-size: $app-font-size-sm;
    color: var(--color-text-muted);
  }
  
  .amount-text {
    font-size: $app-font-size-lg;
    font-weight: bold;
    color: var(--color-primary);
  }

  .record-badge .breath { color: #45C07A; font-weight: 600; }
  
  .record-note {
    padding: $app-spacing-sm;
    background-color: var(--color-bg-section);
    border-radius: $app-button-radius;
  }
  
  .note-text {
    font-size: $app-font-size-sm;
    color: var(--color-text-secondary);
    line-height: 1.4;
  }
  
  .empty-records {
    text-align: center;
    padding: $app-spacing-xl;
  }
  
  .empty-text {
    font-size: $app-font-size-base;
    color: var(--color-text-muted);
  }
  
  .monthly-stats-section {
    padding: $app-spacing-lg;
  }
  
  .stats-header {
    margin-bottom: $app-spacing-lg;
  }
  
  .stats-title {
    font-size: $app-font-size-xl;
    font-weight: bold;
    color: var(--color-text-primary);
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $app-spacing-md;
    margin-bottom: $app-spacing-lg;
  }
  
  .stat-card {
    background-color: var(--color-bg-section);
    padding: $app-spacing-lg;
    border-radius: $app-card-radius;
    text-align: center;
  }
  
  .stat-number {
    font-size: $app-font-size-xxl;
    font-weight: bold;
    color: var(--color-primary);
    display: block;
    margin-bottom: $app-spacing-xs;
  }
  
  .stat-name {
    font-size: $app-font-size-sm;
    color: var(--color-text-muted);
  }
  
  .daily-average {
    text-align: center;
    padding: $app-spacing-md;
    background-color: var(--color-bg-section);
    border-radius: $app-button-radius;
  }
  
  .average-text {
    font-size: $app-font-size-base;
    color: var(--color-text-secondary);
  }
  
  // 悬浮导出按钮与隐藏画布（仅小程序）
  // #ifdef MP-WEIXIN
  .export-fab {
    position: fixed;
    right: 30px;
    bottom: 30px;
    z-index: 99;
    background-color: var(--color-primary);
    color: var(--color-text-white);
    padding: 14rpx 28rpx;
    border-radius: 60rpx;
    box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.15);
  }
  .export-fab-text { font-weight: 700; font-size: $app-font-size-base; }
  .export-canvas {
    position: fixed;
    left: -2000px;
    top: -2000px;
    z-index: -9999;
    background-color: #fff;
  }
  // #endif
  </style>


