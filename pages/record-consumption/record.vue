<template>
  <view class="record-page" :class="currentTheme">
    <!-- 记录历史 -->
    <view class="records-section">
      <view class="section-header">
        <text class="section-title">消费记录</text>
        <text class="record-count">共{{ consumptionRecords.length }}条记录</text>
      </view>
      
      <!-- 日期范围选择器 -->
      <view class="date-picker-section card">
        <uni-datetime-picker
          type="daterange"
          v-model="dateRange"
          rangeSeparator="至"
          @change="onDateRangeChange"
        />
      </view>

      <!-- 筛选选项 -->
      <view class="filter-section card">
        <view class="filter-tabs">
          <view 
            class="filter-tab" 
            :class="{ active: selectedFilter === 'today' }"
            @click="setFilter('today')"
          >
            <text>今天</text>
          </view>
          <view 
            class="filter-tab" 
            :class="{ active: selectedFilter === 'week' }"
            @click="setFilter('week')"
          >
            <text>本周</text>
          </view>
          <view 
            class="filter-tab" 
            :class="{ active: selectedFilter === 'month' }"
            @click="setFilter('month')"
          >
            <text>本月</text>
          </view>
        </view>
      </view>
      
      <!-- 记录列表 -->
      <view class="records-list" v-if="filteredRecords.length > 0">
        <view 
          class="record-item card" 
          v-for="record in filteredRecords" 
          :key="record.id"
        >
          <view class="record-header">
            <view class="record-time-info">
              <text class="record-date">{{ formatRecordDate(record.timestamp) }}</text>
              <text class="record-time">{{ formatRecordTime(record.timestamp) }}</text>
            </view>
            <view class="record-type" :class="record.type">
              {{ record.type === 'cigarette' ? '卷烟' : '电子烟' }}
            </view>
          </view>
          
          <view class="record-content">
            <view class="record-brand-info">
              <text class="brand-name">{{ record.boxId==='__VIRTUAL__' ? '轻清烟盒' : getBrandName(record.boxId) }}</text>
              <text class="box-nickname" v-if="getBoxNickname(record.boxId)">
                ({{ getBoxNickname(record.boxId) }})
              </text>
            </view>
            
            <view class="record-amount">
              <text class="amount-text">
                {{ record.amount }}{{ record.type === 'cigarette' ? '根' : '口' }}
              </text>
              <text class="fake-badge" v-if="record.fake">[呼吸]</text>
            </view>
          </view>
          
          <view class="record-note" v-if="record.note">
            <text class="note-text">{{ record.note }}</text>
          </view>
          
          <view class="record-actions">
            <button class="btn-edit" @click="editRecord(record)">编辑</button>
            <button class="btn-delete" @click="deleteRecord(record)">删除</button>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <view class="iconfont empty-icon">&#xeba7;</view>
        <text class="empty-text">{{ getEmptyText() }}</text>
      </view>
    </view>

    <!-- 编辑记录弹窗 -->
    <CommonModal 
      ref="editRecordPopup" 
      position="center"
      :mask="true"
      :maskClosable="true"
      :animation="false"
      :zIndex="1000"
      :showClose="false"
      :showFooter="false"
    >
      <view class="edit-record-modal card" v-if="editingRecord">
        <view class="modal-header">
          <text class="modal-title">编辑记录</text>
          <view class="modal-close" @click="closeEditModal">×</view>
        </view>
        
        <view class="modal-content">
          <view class="form-row">
            <text class="form-label">品牌:</text>
            <text class="form-value">{{ getBrandName(editingRecord.boxId) }}</text>
          </view>
          
          <view class="form-row">
            <text class="form-label">时间:</text>
            <text class="form-value">{{ formatRecordDateTime(editingRecord.timestamp) }}</text>
          </view>
          
          <view class="form-row">
            <text class="form-label">数量:</text>
            <view class="amount-input">
              <button class="btn-amount" @click="decreaseEditAmount">-</button>
              <input 
                type="number" 
                v-model.number="editAmount" 
                class="amount-value"
                min="1"
                @blur="onEditAmountBlur"
              />
              <button class="btn-amount" @click="increaseEditAmount">+</button>
            </view>
            <text class="amount-unit">{{ editingRecord.type === 'cigarette' ? '根' : '口' }}</text>
          </view>
          
          <view class="form-row">
            <text class="form-label">备注:</text>
            <input 
              class="form-input" 
              v-model="editNote" 
              placeholder="可选备注"
              maxlength="50"
            />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="btn-cancel" @click="closeEditModal">取消</button>
          <button 
            class="btn-confirm btn-primary" 
            @click="saveRecord"
            :disabled="editAmount <= 0"
          >
            保存
          </button>
        </view>
      </view>
    </CommonModal>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { parseToDayjs, getThisWeek, getThisMonth, formatDateOnly } from '../../utils/dateUtils.js'
import CommonModal from '../../components/CommonModal.vue'
import { useTobaccoStore } from '../../composables/useTobaccoStore.js'
import { isToday } from '../../utils/dateUtils.js'
import { STORAGE_KEYS, getStorage, setStorage } from '../../utils/storage.js'
import { useTheme } from '../../composables/useTheme.js'

// 状态管理
const { 
  consumptionRecords, 
  activeBoxes, 
  getBrand, 
  getBoxById, 
  updateConsumptionRecord, 
  removeConsumptionRecord, 
  initializeData,
  fetchRecords,
  setConsumptionRecords
} = useTobaccoStore()

// 本地状态
const selectedFilter = ref(getStorage(STORAGE_KEYS.UI_STATE, { recordFilter: 'today' }).recordFilter || 'today')
const { currentTheme } = useTheme()
const editingRecord = ref(null)
const editAmount = ref(1)
const editNote = ref('')
const editRecordPopup = ref()
const dateRange = ref([]) // ['YYYY-MM-DD', 'YYYY-MM-DD']
const lastPresetFilter = ref(selectedFilter.value) // 记录最近一次非自定义预设

// 页面初始化
onMounted(async () => {
  await initializeData()
  await setFilter(selectedFilter.value)
})

// 计算属性
const filteredRecords = computed(() => {
  // 隐藏 amount === 0 的完成计费记录
  const records = (consumptionRecords.value || []).filter(r => Number(r.amount) > 0)
  // 已按 reloadByFilter 拉取范围，这里只负责排序
  const toTs = (val) => {
    if (typeof val === 'number') return val
    const d = parseToDayjs(val)
    return d.isValid() ? d.valueOf() : 0
  }
  return records.slice().sort((a, b) => toTs(b.timestamp) - toTs(a.timestamp))
})

// 方法定义
const getPresetRange = (filter) => {
  if (filter === 'today') {
    const d = formatDateOnly(new Date())
    const iso = d.replace(/\//g, '-')
    return [iso, iso]
  }
  if (filter === 'week') {
    const range = getThisWeek()
    return [range.start.replace(/\//g, '-'), range.end.replace(/\//g, '-')]
  }
  if (filter === 'month') {
    const range = getThisMonth()
    return [range.start.replace(/\//g, '-'), range.end.replace(/\//g, '-')]
  }
  return [null, null]
}

const fetchByRange = async (startISO, endISO) => {
  if (!startISO || !endISO) return
  try {
    uni.showLoading({ title: '加载中', mask: true })
    const list = await fetchRecords(startISO, endISO)
    setConsumptionRecords(list, 'replace')
  } catch (e) {
    uni.showToast({ title: '加载失败', icon: 'none' })
  } finally {
    try { uni.hideLoading() } catch (e) {}
  }
}

const setFilter = async (filter) => {
  selectedFilter.value = filter
  if (filter !== 'custom') lastPresetFilter.value = filter
  const ui = getStorage(STORAGE_KEYS.UI_STATE, {})
  if (filter !== 'custom') setStorage(STORAGE_KEYS.UI_STATE, { ...ui, recordFilter: filter })
  const [s, e] = getPresetRange(filter)
  if (s && e) {
    dateRange.value = [s, e]
    await fetchByRange(s, e)
  }
}

const reloadByFilter = async (filter) => {
  await setFilter(filter)
}

const onDateRangeChange = async (val) => {
  if (!Array.isArray(val) || val.length !== 2 || !val[0] || !val[1]) {
    const fallback = lastPresetFilter.value || 'today'
    await setFilter(fallback)
    return
  }
  const s = String(val[0])
  const e = String(val[1])
  selectedFilter.value = 'custom'
  dateRange.value = [s, e]
  await fetchByRange(s, e)
}

const getEmptyText = () => {
  switch (selectedFilter.value) {
    case 'today':
      return '今天还没有记录'
    case 'week':
      return '本周还没有记录'
    case 'month':
      return '本月还没有记录'
    default:
      return '暂无记录'
  }
}

const getBrandName = (boxId) => {
  const box = getBoxById(boxId)
  if (!box) return '未知品牌'
  
  const brand = getBrand(box.brandId)
  return brand ? brand.name : '未知品牌'
}

const getBoxNickname = (boxId) => {
  const box = getBoxById(boxId)
  return box ? box.nickname : ''
}

const formatRecordDate = (timestamp) => {
  if (isToday(timestamp)) {
    return '今天'
  }
  return parseToDayjs(timestamp).format('MM-DD')
}

const formatRecordTime = (timestamp) => {
  return parseToDayjs(timestamp).format('HH:mm')
}

const formatRecordDateTime = (timestamp) => {
  return parseToDayjs(timestamp).format('MM-DD HH:mm')
}

const editRecord = (record) => {
  editingRecord.value = record
  editAmount.value = record.amount
  editNote.value = record.note || ''
  editRecordPopup.value.open()
}

const closeEditModal = () => {
  editRecordPopup.value.close()
  editingRecord.value = null
  editAmount.value = 1
  editNote.value = ''
}

const increaseEditAmount = () => {
  editAmount.value += 1
}

const decreaseEditAmount = () => {
  if (editAmount.value > 1) {
    editAmount.value -= 1
  }
}

const onEditAmountBlur = () => {
  if (!editAmount.value || editAmount.value < 1) {
    editAmount.value = 1
  }
}

const saveRecord = () => {
  if (!editingRecord.value || editAmount.value <= 0) return
  
  const updateData = {
    amount: editAmount.value,
    note: editNote.value.trim()
  }
  
  updateConsumptionRecord(editingRecord.value.id, updateData)
  
  uni.showToast({
    title: '记录已更新',
    icon: 'success'
  })
  
  closeEditModal()
}

const deleteRecord = (record) => {
  uni.showModal({
    title: '删除记录',
    content: '确定要删除这条记录吗？删除后无法恢复。',
    confirmText: '删除',
    confirmColor: '#E53E3E',
    success: (res) => {
      if (res.confirm) {
        removeConsumptionRecord(record.id)
        
        uni.showToast({
          title: '记录已删除',
          icon: 'success'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.record-page {
  padding: $app-spacing-md;
  min-height: 100vh;
  background-color: var(--color-bg-page);
}

.records-section {
  
}

.date-picker-section {
  padding: $app-spacing-md;
  margin-bottom: $app-spacing-md;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $app-spacing-md;
}

.section-title {
  font-size: $app-font-size-xl;
  font-weight: bold;
  color: var(--color-text-primary);
}

.record-count {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
}

.filter-section {
  padding: $app-spacing-md;
  margin-bottom: $app-spacing-md;
}

.filter-tabs {
  display: flex;
  gap: $app-spacing-sm;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: $app-spacing-sm;
  background-color: var(--color-bg-section);
  border-radius: $app-button-radius;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  
  &.active {
    background-color: var(--color-primary);
    color: var(--color-text-white);
    border-color: var(--color-primary);
  }
  
  &:active {
    opacity: 0.7;
  }
}

.records-list {
  
}

.record-item {
  padding: $app-spacing-md;
  margin-bottom: $app-spacing-sm;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $app-spacing-sm;
}

.record-time-info {
  
}

.record-date {
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
  font-weight: 500;
  margin-right: $app-spacing-sm;
}

.record-time {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
}

.record-type {
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: $app-font-size-xs;
  color: var(--color-text-white);
  
  &.cigarette {
    background-color: var(--color-secondary);
  }
  
  &.electronic {
    background-color: var(--color-accent);
  }
}

.record-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $app-spacing-sm;
}

.record-brand-info {
  
}

.brand-name {
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
  font-weight: 500;
}

.box-nickname {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
}

.record-amount {
  
}

.amount-text {
  font-size: $app-font-size-lg;
  font-weight: bold;
  color: var(--color-primary);
}
.fake-badge { margin-left: 8rpx; color: #45C07A; font-weight: 600; }

.record-note {
  padding: $app-spacing-sm;
  background-color: var(--color-bg-section);
  border-radius: $app-button-radius;
  margin-bottom: $app-spacing-sm;
}

.note-text {
  font-size: $app-font-size-sm;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.record-actions {
  display: flex;
  gap: $app-spacing-sm;
  justify-content: flex-end;
}

.btn-edit {
  background-color: var(--color-primary);
  color: var(--color-text-white);
  border: none;
  border-radius: $app-button-radius;
  padding: $app-spacing-xs $app-spacing-sm;
  font-size: $app-font-size-sm;
}

.btn-delete {
  background-color: var(--color-danger);
  color: var(--color-text-white);
  border: none;
  border-radius: $app-button-radius;
  padding: $app-spacing-xs $app-spacing-sm;
  font-size: $app-font-size-sm;
}

.empty-state {
  text-align: center;
  padding: $app-spacing-xl;
  margin-top: $app-spacing-xl;
}

.empty-icon {
  font-size: 128rpx;
  margin-bottom: $app-spacing-lg;
}

.empty-text {
  font-size: $app-font-size-lg;
  color: $app-text-muted;
}

/* 深色主题下覆盖 uni-datetime-picker 的样式（外部覆盖） */
.record-page.theme-dark :deep(.uni-date-x) {
  background-color: var(--color-bg-section);
  color: var(--color-text-primary);
}
.record-page.theme-dark :deep(.uni-date-x--border) {
  border-color: var(--color-border);
}
.record-page.theme-dark :deep(.uni-date__x-input) {
  color: var(--color-text-primary);
}
.record-page.theme-dark :deep(.uni-date-range--x),
.record-page.theme-dark :deep(.uni-date-single--x) {
  background-color: var(--color-bg-section);
  border-color: var(--color-border);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.4);
}
.record-page.theme-dark :deep(.popup-x-footer),
.record-page.theme-dark :deep(.uni-date-changed) {
  color: var(--color-text-secondary);
  border-top-color: var(--color-border);
  border-bottom-color: var(--color-border);
}
.record-page.theme-dark :deep(.uni-popper__arrow) {
  border-bottom-color: var(--color-border);
}
.record-page.theme-dark :deep(.uni-popper__arrow::after) {
  border-bottom-color: var(--color-bg-section);
}

/* 清除(删除)按钮在暗色模式下的背景与图标颜色一致化 */
.record-page.theme-dark :deep(.uni-date-editor--x) {
  background-color: var(--color-bg-section);
  border-radius: 4px;
}
.record-page.theme-dark :deep(.uni-date-editor--x .uni-date__icon-clear uni-icons),
.record-page.theme-dark :deep(.uni-date-x .icon-calendar) {
  color: var(--color-text-secondary) !important;
}

.edit-record-modal {
  width: 80vw;
  max-width: 800rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $app-spacing-lg;
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  font-size: $app-font-size-xl;
  font-weight: bold;
  color: var(--color-text-primary);
}

.modal-close {
  font-size: 48rpx;
  color: var(--color-text-muted);
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: $app-spacing-lg;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: $app-spacing-md;
}

.form-label {
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
  margin-right: $app-spacing-sm;
  min-width: 120rpx;
}

.form-value {
  flex: 1;
  font-size: $app-font-size-base;
  color: var(--color-text-secondary);
}

.form-input {
  flex: 1;
  padding: $app-spacing-sm;
  border: 1px solid var(--color-border);
  border-radius: $app-input-radius;
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
}

.amount-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.btn-amount {
  background-color: var(--color-border);
  color: var(--color-text-primary);
  border: none;
  border-radius: $app-button-radius;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-value {
  flex: 1;
  text-align: center;
  margin: 0 $app-spacing-sm;
  padding: $app-spacing-xs;
  border: 1px solid var(--color-border);
  border-radius: $app-input-radius;
  color: var(--color-text-primary);
}

.amount-unit {
  margin-left: $app-spacing-sm;
  color: var(--color-text-muted);
}

.modal-footer {
  display: flex;
  gap: $app-spacing-md;
  padding: $app-spacing-lg;
  border-top: 1px solid var(--color-border);
}

.btn-cancel {
  flex: 1;
  background-color: var(--color-border);
  color: var(--color-text-primary);
  border: none;
  border-radius: $app-button-radius;
  padding: $app-spacing-xs;
  font-size: $app-font-size-base;
}

.btn-confirm {
  flex: 2;
  padding: $app-spacing-xs;
  font-size: $app-font-size-base;
  
  &:disabled {
    background-color: var(--color-text-muted);
  }
}
</style>
