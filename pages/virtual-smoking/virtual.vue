<template>
  <view class="virtual-page" :class="currentTheme">
    <canvas
      canvas-id="vsCanvas"
      id="vsCanvas"
      class="canvas-full"
      type="2d"
      @touchstart="onCanvasTouchStart"
      @touchmove="onCanvasTouchMove"
      @touchend="onCanvasTouchEnd"
      @touchcancel="onCanvasTouchCancel"
    ></canvas>
  </view>
</template>

<script setup>
import { ref, onBeforeUnmount, getCurrentInstance, nextTick } from 'vue'
import { onReady } from '@dcloudio/uni-app'
import { useTheme } from '../../composables/useTheme.js'
import { useTobaccoStore } from '../../composables/useTobaccoStore.js'

const { currentTheme } = useTheme()
const { addVirtualConsumptionRecord } = useTobaccoStore()

const mode = ref('cigarette') // 'cigarette' | 'electronic'
const puffs = ref(0)
const finished = ref(false)

// 常量：单次最长4s、卷烟总时长≈28s（约7次循环）
const PRESS_MAX_SECONDS = 4
const CIG_SECONDS = 28
// 松手后的余光强度（未完成时保留微弱光晕）
const AFTERGLOW_BASE = 0.35
// 碳化带相对燃烧的增长比例（<1 代表更慢的碳化推进）
const CHAR_GROWTH_RATIO = 0.1

// 顶部提示文案状态
const hintVisible = ref(false)
const hintText = ref('')
const hintSecond = ref(0)
let hintHideTimer = null

let canvasNode = null
let ctx = null
let cWidth = 0
let cHeight = 0
let dpr = 1
let rafId = 0
// rAF/cAF 兼容：小程序 appservice 无 window.rAF，这里使用 setTimeout 降级
const _raf = (typeof globalThis !== 'undefined' && typeof globalThis.requestAnimationFrame === 'function')
  ? globalThis.requestAnimationFrame.bind(globalThis)
  : (cb) => setTimeout(() => cb(Date.now()), 16)
const _caf = (typeof globalThis !== 'undefined' && typeof globalThis.cancelAnimationFrame === 'function')
  ? globalThis.cancelAnimationFrame.bind(globalThis)
  : (id) => clearTimeout(id)
let pressTimer = null
let pressStartTs = 0
let currentPressElapsed = 0 // 本次按压累计（秒）
let vibrateTimer = null

// 主按钮一次手势是否始于完成态（用于卷烟完成后点击提交）
let mainPressStartedInFinished = false
// 卷烟是否已燃尽（按住不松手时停止动画/震动，松手再进入完成态）
let cigaretteBurnedOut = false

// 卷烟燃烧进度由总累计时长映射（0~1）
let isPressing = false
let burnProgress = 0
let totalElapsedForCigarette = 0
let pendingAshPx = 0 // 按住过程中累积的悬挂烟灰高度（px）
let charBandPx = 0 // 累积的碳化带高度（px），随燃烧增长并保留

// 烟雾粒子
const smokeParticles = []
const maxParticles = 60
// 电子烟丝状雾：贝塞尔曲线集合与更高的粒子上限（仅电子烟使用）
let vapeBezierTrails = []
const VAPE_MAX_PARTICLES = 380

// 微风参数（缓慢变化的水平风，带轻微方向偏置）
let windTime = 0
let globalWindXPerSec = 0
const windBaseDir = (Math.random() < 0.5 ? -1 : 1) // 初始风向
const windBias = 8 // px/s 基础水平偏置
const windAmp = 22 // px/s 正弦幅度
const windPeriodSec = 9 // 风变化周期（秒）
// 电子烟机身彩虹配色偏移（每次展示固定随机，不动态流动）
let rainbowOffset = Math.random()

const toggleMode = () => {
  mode.value = mode.value === 'cigarette' ? 'electronic' : 'cigarette'
  resetScene()
}

const resetScene = () => {
  finished.value = false
  puffs.value = 0
  burnProgress = 0
  totalElapsedForCigarette = 0
  currentPressElapsed = 0
  pendingAshPx = 0
  charBandPx = 0
  smokeParticles.length = 0
  vapeBezierTrails.length = 0
  ashParticles.length = 0
  ashPile.length = 0
  cigaretteBurnedOut = false
  stopAndHideHint()
  // 每次展示电子烟时，随机一次静态彩虹偏移
  if (mode.value === 'electronic') {
    rainbowOffset = Math.random()
  }
  // 重算 UI 布局与显隐
  try { computeUILayout() } catch (_) {}
}

const initCanvas = () => {
  const proxy = getCurrentInstance()?.proxy
  const query = proxy ? uni.createSelectorQuery().in(proxy) : uni.createSelectorQuery()
  query.select('#vsCanvas').fields({ size: true, node: true }).exec(res => {
    if (!res || !res[0]) return
    const { width, height, node } = res[0]
    canvasNode = node
    dpr = getPixelRatio()
    canvasNode.width = width * dpr
    canvasNode.height = height * dpr
    ctx = canvasNode.getContext('2d')
    ctx.scale(dpr, dpr)
    cWidth = width
    cHeight = height
    computeUILayout()
    startRenderLoop()
  })
}

const startRenderLoop = () => {
  cancelAnim()
  let last = Date.now()
  const loop = () => {
    const now = Date.now()
    const dt = Math.min(100, now - last) / 1000 // 秒
    last = now
    // 更新风场（缓慢变化的水平风速度，正负来回）
    windTime += dt
    const t = windTime % windPeriodSec
    const phase = (t / windPeriodSec) * Math.PI * 2
    globalWindXPerSec = windBaseDir * windBias + Math.sin(phase) * windAmp
    renderFrame(dt)
    rafId = _raf(loop)
  }
  rafId = _raf(loop)
}

const cancelAnim = () => { try { if (rafId) _caf(rafId) } catch(_) {} rafId = 0 }

const spawnSmoke = (x, y, intensity = 1, sizeScale = 1, opts = {}) => {
  const kind = (opts && opts.kind) || ''
  const fine = !!(opts && opts.fine)
  const isVape = kind === 'vape'
  const targetMax = isVape ? VAPE_MAX_PARTICLES : maxParticles
  if (smokeParticles.length >= targetMax) return
  const countBase = fine ? 7 : 4
  const countMultiplier = isVape ? 3.2 : 1
  const count = Math.max(1, Math.floor(countBase * intensity * countMultiplier))
  for (let i = 0; i < count; i++) {
    const baseMin = isVape ? 18 : 16
    const baseMax = isVape ? 36 : 18
    const size = (baseMin + Math.random() * (baseMax - baseMin)) * Math.max(0.1, sizeScale)
    const blobsCount = fine ? (6 + Math.floor(Math.random() * 6)) : (2 + Math.floor(Math.random() * 2))
    const blobShapes = []
    for (let b = 0; b < blobsCount; b++) {
      blobShapes.push({
        ox: (Math.random() - 0.5) * (size * (fine ? 0.9 : 0.6)),
        oy: (Math.random() - 0.5) * (size * (fine ? 0.9 : 0.6)),
        rx: size * (fine ? (0.12 + Math.random() * 0.14) : (0.28 + Math.random() * 0.18)) * (fine ? (0.55 + Math.random() * 0.7) : (1.0 + Math.random() * 1.0)),
        ry: size * (fine ? (0.10 + Math.random() * 0.12) : (0.24 + Math.random() * 0.18)) * (fine ? (0.50 + Math.random() * 0.7) : (0.6 + Math.random() * 0.8)),
        rot: Math.random() * Math.PI
      })
    }
    smokeParticles.push({
      x: x + (Math.random() - 0.5) * 4,
      y: y + (Math.random() - 0.5) * 4,
      // 四向扩散，明显上浮；施加全局风
      vx: (Math.random() - 0.5) * (1.2 + 0.6 * intensity) + (globalWindXPerSec / 60),
      vy: - (0.6 + Math.random() * 0.6) * (0.9 + 0.5 * intensity) + (Math.random() - 0.5) * 0.2,
      alpha: (isVape ? 0.65 : 0.5) + Math.random() * (isVape ? 0.20 : 0.25),
      size,
      drag: fine ? (isVape ? 0.995 : 0.992) : (isVape ? 0.991 : 0.988),
      grow: fine ? (isVape ? (0.10 + Math.random() * 0.03) : (0.12 + Math.random() * 0.18)) : (isVape ? (0.22 + Math.random() * 0.08) : (0.22 + Math.random() * 0.28)),
      life: 0,
      rotSpeed: (Math.random() - 0.5) * 0.01,
      blobShapes
    })
  }
}

// 电子烟丝状雾：生成若干条三次贝塞尔“雾丝”
const spawnVapeBezierSmoke = (x, y, strength = 1.2) => {
  const trails = 12 + Math.floor(Math.random() * 12 * strength)
  for (let i = 0; i < trails; i++) {
    const vy = 50 + Math.random() * 30 // 上浮速度 px/s
    const swayAmp = 16 + Math.random() * 12 // 横向摆动幅度
    const swayFreq = 0.2 + Math.random() * 0.15 // Hz
    const widthBase = 8.0 + Math.random() * 6.0
    const alpha = 0.6 + Math.random() * 0.22
    const maxLife = 2.2 + Math.random() * 1.0 // 秒
    // 初始短段，控制点带少量左右偏移
    const p0 = { x: x + (Math.random() - 0.5) * 10, y: y + (Math.random() - 0.5) * 6 }
    const p3 = { x: p0.x + (Math.random() - 0.5) * 12, y: p0.y - (12 + Math.random() * 18) }
    const p1 = { x: p0.x + (Math.random() - 0.5) * 24, y: p0.y - (6 + Math.random() * 12) }
    const p2 = { x: p3.x + (Math.random() - 0.5) * 24, y: p3.y - (6 + Math.random() * 12) }
    vapeBezierTrails.push({ p0, p1, p2, p3, vy, swayAmp, swayFreq, widthBase, alpha, life: 0, maxLife })
  }
}

// 电子烟丝状雾：更新与绘制（先于粒子雾绘制）
const drawVapeBezierTrails = (dt, topY) => {
  if (!ctx || vapeBezierTrails.length === 0) return
  const toRemove = []
  for (let i = 0; i < vapeBezierTrails.length; i++) {
    const tr = vapeBezierTrails[i]
    const phase = windTime * tr.swayFreq * Math.PI * 2
    // 上浮
    tr.p0.y -= tr.vy * dt; tr.p1.y -= tr.vy * dt; tr.p2.y -= tr.vy * dt; tr.p3.y -= tr.vy * dt
    // 风与摆动
    const sway = tr.swayAmp * Math.sin(phase)
    const windDrift = (globalWindXPerSec / 60)
    tr.p1.x += windDrift + sway * 0.04
    tr.p2.x += windDrift + sway * 0.06
    tr.p3.x += windDrift + sway * 0.08
    // 生命周期
    tr.life += dt
    tr.alpha *= 0.985
    // 超出顶部或寿命结束则回收
    if (tr.alpha < 0.05 || tr.p3.y < Math.max(0, topY - cHeight * 0.3) || tr.life > tr.maxLife) {
      toRemove.push(i)
      continue
    }
    // 分段采样绘制，由粗到细、由强到弱
    const steps = 16
    ctx.save()
    ctx.lineCap = 'round'
    ctx.shadowColor = 'rgba(230,238,246,0.28)'
    ctx.shadowBlur = 4
    for (let s = 0; s < steps; s++) {
      const t0 = s / steps
      const t1 = (s + 1) / steps
      const lerp = (a, b, t) => a + (b - a) * t
      const pointAt = (t) => {
        const mt = 1 - t
        const x = mt*mt*mt*tr.p0.x + 3*mt*mt*t*tr.p1.x + 3*mt*t*t*tr.p2.x + t*t*t*tr.p3.x
        const y = mt*mt*mt*tr.p0.y + 3*mt*mt*t*tr.p1.y + 3*mt*t*t*tr.p2.y + t*t*t*tr.p3.y
        return { x, y }
      }
      const a = pointAt(t0)
      const b = pointAt(t1)
      const width = Math.max(1.2, tr.widthBase * (1 - t1 * 0.5))
      const alpha = Math.max(0, Math.min(1, tr.alpha * (1 - t0) * 0.95))
      ctx.strokeStyle = `rgba(230,238,246,${alpha})`
      ctx.lineWidth = width
      ctx.beginPath(); ctx.moveTo(a.x, a.y); ctx.lineTo(b.x, b.y); ctx.stroke()
    }
    ctx.restore()
  }
  // 按索引逆序删除
  for (let k = toRemove.length - 1; k >= 0; k--) {
    vapeBezierTrails.splice(toRemove[k], 1)
  }
}

// 灰粒与堆积
const ashParticles = []
const ashPile = [] // 到达地面的静态点
const maxAshParticles = 240

// ---- UI：布局、按钮与提示（绘制在 canvas 内）----
let pxPerRpx = 1
let safeBottomPx = 0
let uiPressedId = ''
let uiPressedCanceled = false

// 兼容获取窗口/应用信息，尽量避免 getSystemInfoSync 的弃用告警
const getWindowInfoSafe = () => {
  try {
    if (typeof wx !== 'undefined' && typeof wx.getWindowInfo === 'function') return wx.getWindowInfo()
  } catch (_) {}
  try {
    if (typeof uni !== 'undefined' && typeof uni.getWindowInfo === 'function') return uni.getWindowInfo()
  } catch (_) {}
  try {
    // 兜底：旧端可能只有 getSystemInfoSync（可能伴随告警）
    return uni.getSystemInfoSync()
  } catch (_) { return null }
}

const getAppBaseInfoSafe = () => {
  try {
    if (typeof wx !== 'undefined' && typeof wx.getAppBaseInfo === 'function') return wx.getAppBaseInfo()
  } catch (_) {}
  try {
    if (typeof uni !== 'undefined' && typeof uni.getAppBaseInfo === 'function') return uni.getAppBaseInfo()
  } catch (_) {}
  return null
}

const getPixelRatio = () => {
  try {
    const wi = getWindowInfoSafe()
    if (wi && typeof wi.pixelRatio === 'number') return wi.pixelRatio
  } catch (_) {}
  return 1
}

const uiButtons = {
  toggle: {
    id: 'toggle', x: 0, y: 0, w: 0, h: 0, r: 12,
    visible: true, pressed: false,
    getText: () => mode.value === 'cigarette' ? '电子烟' : '卷烟',
    bg: '#374151', fg: '#ffffff'
  },
  main: {
    id: 'main', x: 0, y: 0, w: 0, h: 0, r: 16,
    visible: true, pressed: false,
    getText: () => (finished.value ? '完成' : '吸气'),
    bg: '#10b981', fg: '#ffffff'
  },
  done: {
    id: 'done', x: 0, y: 0, w: 0, h: 0, r: 12,
    visible: false, pressed: false,
    getText: () => '完成',
    bg: '#6366f1', fg: '#ffffff'
  }
}

const computeUILayout = () => {
  // rpx->px 转换与安全区
  pxPerRpx = cWidth > 0 ? (cWidth / 750) : 1
  try {
    const info = getWindowInfoSafe()
    if (info && info.safeArea && typeof info.screenHeight === 'number') {
      // bottom inset = screenHeight - safeArea.bottom
      safeBottomPx = Math.max(0, info.screenHeight - info.safeArea.bottom)
    } else if (info && info.safeArea && typeof info.windowHeight === 'number') {
      // 回退：用 windowHeight 估算
      safeBottomPx = Math.max(0, info.windowHeight - info.safeArea.bottom)
    } else {
      safeBottomPx = 0
    }
  } catch (_) { safeBottomPx = 0 }

  // 尺寸与边距
  const bottomGap = 40 * pxPerRpx + safeBottomPx
  const toggleHeight = 72 * pxPerRpx
  const doneHeight = 72 * pxPerRpx
  const mainDiameter = 84 * pxPerRpx // 主按钮改为小号圆形

  // 主按钮（右下）
  const marginX = 40 * pxPerRpx
  const verticalGap = 16 * pxPerRpx
  uiButtons.main.h = mainDiameter
  uiButtons.main.w = mainDiameter
  uiButtons.main.x = cWidth - marginX - uiButtons.main.w
  uiButtons.main.y = cHeight - bottomGap - uiButtons.main.h

  // 模式切换（左下，椭圆）
  uiButtons.toggle.h = toggleHeight
  uiButtons.toggle.w = 200 * pxPerRpx
  uiButtons.toggle.x = 40 * pxPerRpx
  uiButtons.toggle.y = cHeight - bottomGap - uiButtons.toggle.h

  // 电子烟完成（右下主按钮上方）
  // 电子烟完成按钮同样使用圆形尺寸
  uiButtons.done.h = mainDiameter
  uiButtons.done.w = mainDiameter
  uiButtons.done.x = uiButtons.main.x
  uiButtons.done.y = uiButtons.main.y - (verticalGap * 3) - uiButtons.done.h

  // 可见性：仅电子烟 >=5 口 显示独立完成按钮（卷烟使用主按钮）
  const elecReady = (mode.value === 'electronic' && puffs.value >= 5)
  uiButtons.done.visible = elecReady

  // 椭圆半径（胶囊）：高度的一半
  uiButtons.main.r = uiButtons.main.h / 2
  uiButtons.done.r = uiButtons.done.h / 2
  uiButtons.toggle.r = uiButtons.toggle.h / 2
}

const drawButton = (btn) => {
  if (!btn.visible) return
  const scale = btn.pressed ? 0.98 : 1
  const x = btn.x + (btn.w * (1 - scale)) / 2
  const y = btn.y + (btn.h * (1 - scale)) / 2
  const w = btn.w * scale
  const h = btn.h * scale

  // 背景
  ctx.save()
  ctx.fillStyle = btn.bg
  ctx.shadowColor = btn.pressed ? 'rgba(0,0,0,0.25)' : 'rgba(0,0,0,0.35)'
  ctx.shadowBlur = btn.pressed ? 6 : 10
  ctx.shadowOffsetY = btn.pressed ? 2 : 4
  drawRoundRect(x, y, w, h, btn.r); ctx.fill()
  ctx.restore()

  // 文本
  const fontSize = (btn.id === 'main' ? 18 : 14) * pxPerRpx
  ctx.fillStyle = btn.fg
  ctx.font = `600 ${Math.max(12, fontSize)}px sans-serif`
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText(btn.getText(), x + w / 2, y + h / 2)
}

const drawButtons = () => {
  // 绘制顺序影响视觉叠放与命中优先
  drawButton(uiButtons.main)
  drawButton(uiButtons.done)
  drawButton(uiButtons.toggle)
}

const drawHintBanner = () => {
  if (!hintVisible.value) return
  const topY = 40 * pxPerRpx
  // 随时间放大：剩余时间减少，放大增加
  const elapsedSteps = Math.min(4, Math.max(0, 4 - (hintSecond.value || 0)))
  const scale = 1 + 0.12 * elapsedSteps
  const text = hintText.value || ''

  ctx.save()
  ctx.translate(cWidth / 2, topY)
  ctx.scale(scale, scale)
  ctx.fillStyle = 'rgba(255,255,255,0.92)'
  const fontSize = 16 * pxPerRpx
  ctx.font = `600 ${Math.max(12, fontSize)}px sans-serif`
  ctx.textAlign = 'center'
  ctx.textBaseline = 'top'
  // 轻微阴影
  ctx.shadowColor = 'rgba(0,0,0,0.35)'
  ctx.shadowBlur = 6
  ctx.fillText(text, 0, 0)
  ctx.restore()
}

const pointInRect = (x, y, r) => (x >= r.x && x <= r.x + r.w && y >= r.y && y <= r.y + r.h)

const getTopmostButtonAt = (x, y) => {
  // 优先主按钮、右下完成、左下切换
  if (uiButtons.main.visible && pointInRect(x, y, uiButtons.main)) return uiButtons.main
  if (uiButtons.done.visible && pointInRect(x, y, uiButtons.done)) return uiButtons.done
  if (uiButtons.toggle.visible && pointInRect(x, y, uiButtons.toggle)) return uiButtons.toggle
  return null
}

const abortPress = () => {
  // 取消一次按压，不计入业务逻辑
  if (isPressing) {
    isPressing = false
  }
  if (pressTimer) { try { clearInterval(pressTimer) } catch(_) {} pressTimer = null }
  currentPressElapsed = 0
}

const onCanvasTouchStart = (e) => {
  if (!ctx) return
  const t = e.touches && e.touches[0]
  if (!t) return
  const x = t.x
  const y = t.y
  const hit = getTopmostButtonAt(x, y)
  uiPressedCanceled = false
  if (hit) {
    uiPressedId = hit.id
    hit.pressed = true
    if (hit.id === 'main') {
      // 记录该次手势开始时是否处于完成态
      mainPressStartedInFinished = finished.value
      if (!finished.value) {
        onPressStart()
      }
    }
  } else {
    uiPressedId = ''
  }
}

const onCanvasTouchMove = (e) => {
  if (!uiPressedId) return
  const t = e.touches && e.touches[0]
  if (!t) return
  const x = t.x
  const y = t.y
  const btn = uiButtons[uiPressedId]
  if (!btn) return
  if (!pointInRect(x, y, btn)) {
    // 滑出：标记取消；若是主按钮长按，终止但不计入业务
    uiPressedCanceled = true
    btn.pressed = false
    if (uiPressedId === 'main' && !finished.value) {
      abortPress()
    }
  } else {
    btn.pressed = true
  }
}

const onCanvasTouchEnd = (e) => {
  const id = uiPressedId
  uiPressedId = ''
  if (!id) return
  const btn = uiButtons[id]
  if (btn) btn.pressed = false

  if (uiPressedCanceled) { uiPressedCanceled = false; return }

  if (id === 'main') {
    if (mainPressStartedInFinished) {
      // 完成态下的点击：提交
      confirmFinish()
    } else if (!finished.value) {
      // 非完成态：结束一次吸气
      onPressEnd()
    } else {
      // 手势开始时未完成，但过程中燃尽；此处不自动提交
    }
    mainPressStartedInFinished = false
  } else if (id === 'toggle') {
    // 切换震动一次
    try { if (typeof uni.vibrateShort === 'function') uni.vibrateShort({}) } catch(_) {}
    toggleMode(); computeUILayout()
  } else if (id === 'done') {
    // 二次确认
    uni.showModal({
      title: '确认完成',
      content: '确认完成本次模拟？',
      success: (res) => { if (res.confirm) confirmFinish() }
    })
  }
}

const onCanvasTouchCancel = () => {
  if (uiButtons.main.pressed && !finished.value) {
    abortPress()
  }
  uiButtons.toggle.pressed = false
  uiButtons.main.pressed = false
  uiButtons.done.pressed = false
  uiPressedId = ''
  uiPressedCanceled = false
  mainPressStartedInFinished = false
}

const spawnAshFall = (x, y) => {
  const count = 24 + Math.floor(Math.random() * 20)
  for (let i = 0; i < count && ashParticles.length < maxAshParticles; i++) {
    ashParticles.push({
      x: x + (Math.random() - 0.5) * 16,
      y: y + (Math.random() - 0.5) * 4,
      vx: (Math.random() - 0.5) * 0.6,
      vy: 0.2 + Math.random() * 0.6,
      ay: 0.05 + Math.random() * 0.05,
      alpha: 0.9,
      size: 1 + Math.random() * 2,
      rest: false
    })
  }
}

const drawAshSystem = (groundY) => {
  // 落体
  for (let i = ashParticles.length - 1; i >= 0; i--) {
    const p = ashParticles[i]
    if (!p.rest) {
      p.vy += p.ay
      p.x += p.vx
      p.y += p.vy
      // 空中绘制粒子
      const a = Math.max(0, Math.min(1, p.alpha))
      if (a > 0.02) {
        ctx.fillStyle = `rgba(150,156,162,${a})`
        ctx.beginPath(); ctx.arc(p.x, p.y, Math.max(0.5, p.size * 0.6), 0, Math.PI * 2); ctx.fill()
        p.alpha *= 0.996
      }
      if (p.y >= groundY) {
        p.rest = true
        p.y = groundY
        ashPile.push({ x: p.x, y: p.y, alpha: 0.9, size: p.size * 2.2 })
        ashPile.push({ x: p.x + (Math.random() - 0.5) * 2, y: p.y, alpha: 0.7, size: p.size * 1.6 })
        ashParticles.splice(i, 1)
        continue
      }
    }
  }
  // 绘制堆积
  for (let i = ashPile.length - 1; i >= 0; i--) {
    const a = ashPile[i]
    a.alpha *= 0.999
    if (a.alpha < 0.05) { ashPile.splice(i, 1); continue }
    // 颗粒感：用小颗粒点状叠加
    ctx.fillStyle = `rgba(150, 156, 162, ${a.alpha})`
    const gran = Math.max(2, Math.floor(a.size))
    for (let gx = 0; gx < gran; gx++) {
      for (let gy = 0; gy < Math.max(1, Math.floor(a.size * 0.6)); gy++) {
        const jitterX = (Math.random() - 0.5) * 0.6
        const jitterY = (Math.random() - 0.5) * 0.6
        ctx.fillRect(a.x + gx + jitterX, a.y + gy + jitterY, 0.8, 0.8)
      }
    }
  }
}

// 将一段碳化带“爆裂”为大量细碎灰粒并下落
const spawnCharShatter = (xCenter, topY, stickW, charHeight) => {
  const w = Math.max(8, stickW - 10)
  const h = Math.max(4, charHeight)
  const pieces = 24 + Math.floor(Math.random() * 36)
  for (let i = 0; i < pieces && ashParticles.length < maxAshParticles; i++) {
    const px = xCenter + (Math.random() - 0.5) * (w * 0.9)
    const py = topY + (Math.random() - 0.5) * (h * 0.4)
    ashParticles.push({
      x: px,
      y: py,
      vx: (Math.random() - 0.5) * 1.2, // 左右随机飞散
      vy: 0.2 + Math.random() * 0.8,
      ay: 0.06 + Math.random() * 0.06,
      alpha: 0.9,
      size: 1 + Math.random() * 1.8,
      rest: false
    })
  }
}

// 工具：绘制圆角矩形（兼容不支持 roundRect 的环境）
const drawRoundRect = (x, y, w, h, r) => {
  const rr = Math.min(r, w / 2, h / 2)
  ctx.beginPath()
  ctx.moveTo(x + rr, y)
  ctx.lineTo(x + w - rr, y)
  ctx.quadraticCurveTo(x + w, y, x + w, y + rr)
  ctx.lineTo(x + w, y + h - rr)
  ctx.quadraticCurveTo(x + w, y + h, x + w - rr, y + h)
  ctx.lineTo(x + rr, y + h)
  ctx.quadraticCurveTo(x, y + h, x, y + h - rr)
  ctx.lineTo(x, y + rr)
  ctx.quadraticCurveTo(x, y, x + rr, y)
  ctx.closePath()
}

const renderFrame = (dt) => {
  if (!ctx) return
  // 纯黑背景
  ctx.fillStyle = '#000'
  ctx.fillRect(0, 0, cWidth, cHeight)

  const cx = cWidth / 2
  const filterH = 110 // 更长滤嘴
  const stickW = 40
  const topY = cHeight * 0.14 // 顶部留出提示区域
  const bottomY = cHeight * 0.88
  const groundY = bottomY + 6

  // 卷烟长度（不包含滤嘴）
  const fullStickH = (bottomY - topY) - filterH
  if (mode.value === 'cigarette') {
    // 按压时先更新累计时长，再计算进度，避免一帧滞后
    if (isPressing && !finished.value && !cigaretteBurnedOut) {
      const now = Date.now()
      currentPressElapsed = Math.min(PRESS_MAX_SECONDS, (now - pressStartTs) / 1000)
      const burnRatePxPerSec = fullStickH / CIG_SECONDS
      const dtClamped = Math.min(dt, 0.1)
      const burnDeltaPx = burnRatePxPerSec * dtClamped
      totalElapsedForCigarette = Math.min(CIG_SECONDS, totalElapsedForCigarette + dtClamped)
      pendingAshPx += burnDeltaPx
      // 碳化带推进速度低于燃烧速度，避免后半段大面积碳化
      charBandPx += burnDeltaPx * CHAR_GROWTH_RATIO
      // 更新提示
      updateHintDuringPress(currentPressElapsed)
    }
    // 依据最新累计时长映射燃烧进度
    burnProgress = Math.min(1, totalElapsedForCigarette / CIG_SECONDS)
    // 卷烟在按住期间若燃尽：停止动画增量与震动，但不立刻进入完成态
    if (!cigaretteBurnedOut && burnProgress >= 1) {
      cigaretteBurnedOut = true
      if (vibrateTimer) { try { clearInterval(vibrateTimer) } catch(_) {} vibrateTimer = null }
    }
    // 用最新的 tipY 生成烟雾，确保跟随烟头前缘（燃尽后不再生成）
    if (isPressing && !finished.value && !cigaretteBurnedOut) {
      const ashHForTip = Math.max(2, pendingAshPx)
      const tipYForSmoke = topY + (fullStickH * burnProgress) - ashHForTip
      spawnSmoke(cx + (Math.random() - 0.5) * 4, tipYForSmoke + 2, 1)
    }
  } else if (mode.value === 'electronic') {
    if (isPressing) {
      const now = Date.now()
      currentPressElapsed = Math.min(PRESS_MAX_SECONDS, (now - pressStartTs) / 1000)
      updateHintDuringPress(currentPressElapsed)
    }
  }
  const burnedH = fullStickH * burnProgress
  const currentTop = topY + burnedH

  if (mode.value === 'cigarette') {
    // 纸杆（剩余）
    ctx.fillStyle = '#F1F5F9'
    ctx.fillRect(cx - stickW/2, currentTop, stickW, fullStickH - burnedH)

    // 滤嘴
    const grd = ctx.createLinearGradient(cx - stickW/2, bottomY - filterH, cx - stickW/2, bottomY)
    grd.addColorStop(0, '#D7A85D')
    grd.addColorStop(1, '#B4843E')
    ctx.fillStyle = grd
    ctx.fillRect(cx - stickW/2, bottomY - filterH, stickW, filterH)

    // 灰烬帽（随 pendingAshPx 生长，按真实燃烧不断变长）
    const ashH = Math.max(2, pendingAshPx)
    const tipY = currentTop - ashH
    ctx.fillStyle = '#BFC5CA'
    ctx.fillRect(cx - stickW/2, currentTop - ashH, stickW, ashH)
    // 灰烬噪点
    ctx.fillStyle = 'rgba(80,90,100,0.6)'
    for (let i = 0; i < 24; i++) {
      const rx = cx - stickW/2 + 4 + Math.random() * (stickW - 8)
      const ry = currentTop - ashH + Math.random() * Math.max(1, ashH - 2)
      ctx.fillRect(rx, ry, 1, 1)
    }
    // 碳化带：灰烬下方的碳化纸张，随燃烧增长
    const availableBelow = Math.max(0, (bottomY - filterH) - currentTop)
    const charH = Math.max(2, Math.min(charBandPx, availableBelow))
    const charGrad = ctx.createLinearGradient(cx, currentTop, cx, currentTop + charH)
    charGrad.addColorStop(0, 'rgba(210,170,120,0.65)')
    charGrad.addColorStop(1, 'rgba(160,120,80,0.35)')
    ctx.fillStyle = charGrad
    ctx.fillRect(cx - stickW/2, currentTop, stickW, charH)
    // 火点椭圆光圈：围绕碳化带上缘（currentTop），松手保留余光；完成时熄灭
    const glowStrength = finished.value ? 0 : (isPressing ? 1 : AFTERGLOW_BASE)
    if (glowStrength > 0) {
      ctx.save()
      ctx.translate(cx, currentTop)
      ctx.scale(1.5, 1.0) // 横向拉伸营造椭圆
      const g2 = ctx.createRadialGradient(0, 0, 8, 0, 0, 24)
      g2.addColorStop(0, `rgba(255,150,90,${0.55 * glowStrength})`)
      g2.addColorStop(0.45, `rgba(255,130,70,${0.38 * glowStrength})`)
      g2.addColorStop(1, `rgba(255,120,60,${0.05 * glowStrength})`)
      ctx.fillStyle = g2
      ctx.beginPath(); ctx.arc(0, 0, 24, 0, Math.PI * 2); ctx.fill()
      ctx.restore()
    }
  }

  if (mode.value === 'electronic') {
    // 电子烟造型
    const deviceH = (bottomY - topY) * 0.85
    // 整体下移一点，避免过于贴近顶部
    const offsetY = cHeight * 0.04
    const eTop = topY + offsetY
    const bodyW = 44
    // 机身顶部留出嘴部缝隙，避免被机身覆盖
    const mouthGap = 12
    const bodyTopY = eTop + mouthGap
    const bodyH = Math.max(0, deviceH - mouthGap)
    // 主体
    const grdBody = ctx.createLinearGradient(cx - bodyW/2, bodyTopY, cx - bodyW/2, bodyTopY + bodyH)
    const rainbow = ['#FF3E3E','#FF8A00','#FFD200','#3BD400','#00C2FF','#5866FF','#C03AFF']
    // 无缝滚动：复制首色至末尾，并在0与1加同色端点，避免中段“切换横线”
    const extended = [...rainbow, rainbow[0]]
    const N = extended.length - 1
    for (let i = 0; i <= N; i++) {
      const base = i / N
      const pos = (base + rainbowOffset) % 1
      grdBody.addColorStop(pos, extended[i])
    }
    // 额外两端固定端点，进一步平滑边界
    grdBody.addColorStop(0, extended[0])
    grdBody.addColorStop(1, extended[extended.length - 1])
    ctx.fillStyle = grdBody
    ctx.fillRect(cx - bodyW/2, bodyTopY, bodyW, bodyH)
    // 顶部圆角嘴（更亮的银灰渐变，并描边以增强对比）
    const mouthGrad = ctx.createLinearGradient(cx, eTop - 20, cx, eTop)
    mouthGrad.addColorStop(0, '#ECEFF1')
    mouthGrad.addColorStop(0.5, '#C9CED3')
    mouthGrad.addColorStop(1, '#9AA2AA')
    ctx.fillStyle = mouthGrad
    ctx.beginPath()
    ctx.moveTo(cx - bodyW/2, eTop)
    ctx.quadraticCurveTo(cx, eTop - 20, cx + bodyW/2, eTop)
    ctx.closePath(); ctx.fill()
    ctx.strokeStyle = 'rgba(255,255,255,0.35)'
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(cx - bodyW/2, eTop)
    ctx.quadraticCurveTo(cx, eTop - 20, cx + bodyW/2, eTop)
    ctx.closePath(); ctx.stroke()
    // 指示灯窗口
    const winH = 36, winW = 26
    const winY = bodyTopY + bodyH * 0.45
    ctx.fillStyle = '#1f1f1f'
    ctx.beginPath(); ctx.ellipse(cx, winY, winW/2, winH/2, 0, 0, Math.PI*2); ctx.fill()
    // 指示灯
    if (isPressing) {
      const lg = ctx.createLinearGradient(cx, winY - 10, cx, winY + 10)
      lg.addColorStop(0, 'rgba(190, 150, 255, 1)')
      lg.addColorStop(1, 'rgba(130, 90, 230, 1)')
      ctx.fillStyle = lg
      ctx.beginPath()
      drawRoundRect(cx - 10, winY - 6, 20, 12, 6)
      ctx.fill()
    }
  }

  // 电子烟雾丝（先绘制丝状，再绘制粒子雾）
  if (mode.value === 'electronic') {
    drawVapeBezierTrails(dt, topY)
  }
  // 烟雾粒子
  for (let i = smokeParticles.length - 1; i >= 0; i--) {
    const p = smokeParticles[i]
    // 轻微拖拽衰减，加入全局风的水平分量
    p.vx += (globalWindXPerSec / 60) * 0.35
    p.vx *= p.drag || 0.985
    p.vy *= p.drag || 0.985
    p.x += p.vx
    p.y += p.vy
    p.alpha *= 0.982
    // 逐步增大烟团，使扩散更明显
    p.size *= 1 + (p.grow || 0.2) * 0.016
    p.life += 1
    // 提升向上覆盖范围（可达画布高度 ~70%）
    const topCoverage = cHeight * 0.3
    if (p.alpha < 0.03 || p.y < Math.max(topY - topCoverage, 0)) { smokeParticles.splice(i, 1); continue }
    // 使用不规则团块：多重小椭圆叠加
    ctx.fillStyle = `rgba(220,230,240,${p.alpha})`
    const shapes = p.blobShapes || []
    for (let b = 0; b < shapes.length; b++) {
      const s = shapes[b]
      // 缓慢旋转，避免快速抖动
      s.rot += (p.rotSpeed || 0)
      const ox = s.ox
      const oy = s.oy
      const rx = s.rx
      const ry = s.ry
      ctx.save()
      ctx.translate(p.x + ox, p.y + oy)
      ctx.rotate(s.rot)
      ctx.beginPath(); ctx.ellipse(0, 0, rx, ry, 0, 0, Math.PI * 2); ctx.fill()
      ctx.restore()
    }
  }

  // 地面虚线/阴影（承接灰堆）
  ctx.strokeStyle = 'rgba(255,255,255,0.06)'
  ctx.beginPath()
  ctx.moveTo(cx - 120, groundY)
  ctx.lineTo(cx + 120, groundY)
  ctx.stroke()

  // 烟灰系统与地面
  drawAshSystem(groundY)
  //（移除整块条下落）碳化带改为细碎灰粒爆裂下落

  // 完成判定
  // 卷烟不再自动提交，仅在燃尽后显示“完成”按钮由用户确认

  // ---- UI 层（提示与按钮）----
  // 动态更新电子烟完成按钮显隐
  uiButtons.done.visible = (mode.value === 'electronic' && puffs.value >= 5)
  drawHintBanner()
  drawButtons()
}

const onPressStart = () => {
  if (finished.value) return
  isPressing = true
  pressStartTs = Date.now()
  currentPressElapsed = 0
  startAndShowHint()
  // 震动：长按期间持续轻微震动（节流）
  try { if (typeof uni.vibrateShort === 'function') uni.vibrateShort({}) } catch(_) {}
  if (!vibrateTimer) {
    vibrateTimer = setInterval(() => {
      try { if (isPressing && typeof uni.vibrateShort === 'function') uni.vibrateShort({}) } catch(_) {}
    }, 180)
  }
  if (mode.value === 'electronic') {
    pressTimer = setInterval(() => {
      const now = Date.now()
      // 电子烟：计时仅在松手时加1口，此处用于节流烟雾或其他用途
    }, 80)
  }
}

const onPressEnd = () => {
  if (!isPressing) return
  isPressing = false
  if (pressTimer) { clearInterval(pressTimer); pressTimer = null }
  if (vibrateTimer) { try { clearInterval(vibrateTimer) } catch(_) {} vibrateTimer = null }

  const now = Date.now()
  const elapsed = Math.min(PRESS_MAX_SECONDS, (now - pressStartTs) / 1000)

  if (mode.value === 'cigarette') {
    // 卷烟：松手时将悬挂烟灰转为掉落粒子
    const cx = cWidth / 2
    const topY = cHeight * 0.14
    const bottomY = cHeight * 0.88
    const filterH = 110
    const fullStickH = (bottomY - topY) - filterH
    const burnedH = fullStickH * Math.min(1, totalElapsedForCigarette / CIG_SECONDS)
    const currentTop = topY + burnedH
    // 按悬挂高度决定掉落粒子数量强度；掉落后清零悬挂
    const dropTimes = Math.max(1, Math.floor(pendingAshPx / 4))
    for (let i = 0; i < dropTimes; i++) spawnAshFall(cx, currentTop)
    pendingAshPx = 0
    // 保留碳化带，不再清零或整体下落
    showExhaleHintBrief()
    // 若在按住期间已经燃尽，则在松手时进入完成态并隐藏提示
    if (cigaretteBurnedOut && !finished.value) {
      finished.value = true
      stopAndHideHint()
    }
  } else {
    // 电子烟：一次吸气结束计为一口，呼气时喷雾（放大为原来的5倍 ≈ +400%）
    puffs.value += 1
    const cx = cWidth / 2
    const topY = cHeight * 0.14
    // 先生成丝状雾（主骨架），更粗更明显
    spawnVapeBezierSmoke(cx, topY + 14, 1.4)
    // 再多批次生成细雾团（更大尺寸、更高数量，透明度更高），提升“烟雾感”
    spawnSmoke(cx + (Math.random() - 0.5) * 6, topY + 10, 4.2, 1.4, { fine: true, kind: 'vape' })
    spawnSmoke(cx + (Math.random() - 0.5) * 10, topY + 10, 3.8, 1.3, { fine: true, kind: 'vape' })
    spawnSmoke(cx + (Math.random() - 0.5) * 14, topY + 12, 3.2, 1.2, { fine: true, kind: 'vape' })
    showExhaleHintBrief()
  }
  currentPressElapsed = 0
}


const confirmFinish = async () => {
  try {
    const amount = mode.value === 'cigarette' ? 1 : Math.max(1, puffs.value)
    uni.showLoading({ title: '记录中', mask: true })
    await addVirtualConsumptionRecord({ type: mode.value, amount })
    uni.hideLoading()
    uni.showToast({ title: '恭喜你，刚刚把冲动化成了呼吸', icon: 'success' })
    setTimeout(() => { uni.navigateBack() }, 600)
  } catch (e) {
    try { uni.hideLoading() } catch (_) {}
    uni.showToast({ title: '记录失败', icon: 'none' })
  }
}

// 顶部提示控制
const startAndShowHint = () => {
  hintVisible.value = true
  hintText.value = '深呼吸，坚持4秒'
  hintSecond.value = 0
  if (hintHideTimer) { clearTimeout(hintHideTimer); hintHideTimer = null }
}
const updateHintDuringPress = (sec) => {
  // 倒计时：从 4s 递减到 0，文本为“深吸气，长按N秒”
  const remaining = Math.max(0, 4 - Math.floor(sec))
  hintSecond.value = remaining
  if (remaining <= 0) {
    hintText.value = '吐气～'
  } else {
    hintText.value = `深吸气，长按${remaining}秒`
  }
}
const showExhaleHintBrief = () => {
  hintVisible.value = true
  hintText.value = '吐气～'
  hintSecond.value = 4
  if (hintHideTimer) clearTimeout(hintHideTimer)
  hintHideTimer = setTimeout(() => { stopAndHideHint() }, 600)
}
const stopAndHideHint = () => {
  if (hintHideTimer) { clearTimeout(hintHideTimer); hintHideTimer = null }
  hintVisible.value = false
  hintSecond.value = 0
}

onReady(() => {
  nextTick(() => { initCanvas(); resetScene() })
})

onBeforeUnmount(() => {
  if (pressTimer) clearInterval(pressTimer)
  cancelAnim()
  if (hintHideTimer) clearTimeout(hintHideTimer)
})
</script>

<style lang="scss" scoped>
.virtual-page {
  position: relative;
  min-height: 100vh;
  background: #000;
}

.canvas-full {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 0;
}

.hint-overlay {
  position: fixed;
  top: 40rpx;
  left: 0; right: 0;
  display: flex;
  justify-content: center;
  pointer-events: none;
  z-index: 3;
}
.hint-text {
  color: rgba(255,255,255,0.9);
  font-size: 32rpx;
  font-weight: 600;
  transition: transform 200ms ease;
}

.main-btn {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: calc(40rpx + constant(safe-area-inset-bottom));
  bottom: calc(40rpx + env(safe-area-inset-bottom));
  height: 96rpx; line-height: 96rpx; font-size: $app-font-size-lg;
  padding: 0 48rpx;
  background: #10b981; /* teal-500 */
  color: #fff;
  border: none;
  border-radius: $app-button-radius;
  box-shadow: 0 6rpx 20rpx rgba(16,185,129,0.35);
  z-index: 4;
}
.toggle-btn {
  position: fixed;
  left: 40rpx;
  bottom: calc(40rpx + constant(safe-area-inset-bottom));
  bottom: calc(40rpx + env(safe-area-inset-bottom));
  height: 72rpx; line-height: 72rpx;
  font-size: $app-font-size-sm;
  background: #374151; /* slate-700 */
  color: #fff;
  border: none;
  padding: 0 28rpx;
  border-radius: $app-button-radius;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.35);
  z-index: 4;
}
.done-btn {
  position: fixed;
  right: 40rpx;
  bottom: calc(40rpx + constant(safe-area-inset-bottom));
  bottom: calc(40rpx + env(safe-area-inset-bottom));
  height: 72rpx; line-height: 72rpx;
  font-size: $app-font-size-sm;
  padding: 0 28rpx;
  background: #6366f1; /* indigo-500 */
  color: #fff;
  border: none;
  border-radius: $app-button-radius;
  box-shadow: 0 4rpx 12rpx rgba(99,102,241,0.35);
  z-index: 4;
}
</style>


