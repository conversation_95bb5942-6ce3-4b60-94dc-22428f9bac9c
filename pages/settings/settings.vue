<template>
  <view class="settings-page" :class="currentTheme">
    <!-- 烟盒管理设置 -->
    <view class="settings-section card">
      <view class="section-header">
        <text class="section-title">烟盒管理</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">最大活跃烟盒数量</text>
        <view class="setting-control">
          <button class="btn-amount" @click="decreaseMaxBoxes">-</button>
          <text class="amount-display">{{ userSettings.maxActiveBoxes }}</text>
          <button class="btn-amount" @click="increaseMaxBoxes">+</button>
        </view>
      </view>
      
      <!-- <view class="setting-item">
        <text class="setting-label">提醒功能</text>
        <switch 
          :checked="userSettings.reminderEnabled" 
          @change="onReminderChange"
          color="#2C5282"
        />
      </view> -->
    </view>

    <!-- 主题设置 -->
    <view class="settings-section card">
      <view class="section-header">
        <text class="section-title">主题</text>
      </view>

      <view class="setting-item">
        <text class="setting-label">外观</text>
        <view class="setting-control">
          <button class="btn btn--sm" :class="{ 'btn--primary': currentTheme === 'theme-light' }" @click="changeTheme('light')">亮色</button>
          <button class="btn btn--sm" :class="{ 'btn--primary': currentTheme === 'theme-dark' }" @click="changeTheme('dark')">暗色</button>
        </view>
      </view>
    </view>

    <!-- 默认规格设置 -->
    <view class="settings-section card">
      <view class="section-header">
        <text class="section-title">默认规格</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">卷烟每盒数量</text>
        <view class="setting-control">
          <input 
            class="setting-input" 
            type="number" 
            v-model.number="userSettings.defaultCigaretteCount"
            @blur="saveSettings"
          />
          <text class="setting-unit">根</text>
        </view>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">电子烟每颗烟弹口数</text>
        <view class="setting-control">
          <input 
            class="setting-input" 
            type="number" 
            v-model.number="userSettings.defaultElectronicPuffs"
            @blur="saveSettings"
          />
          <text class="setting-unit">口</text>
        </view>
      </view>
    </view>

    <!-- 每日限制设置 -->
    <view class="settings-section card">
      <view class="section-header">
        <text class="section-title">每日限制</text>
        <text class="section-desc">设置每日抽烟上限，帮助控制用量</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">启用每日限制</text>
        <switch 
          :checked="userSettings.dailyLimit?.enabled || false" 
          @change="onDailyLimitToggle"
          color="#2C5282"
        />
      </view>
      
      <view v-if="userSettings.dailyLimit?.enabled">
        <view class="setting-item">
          <text class="setting-label">卷烟限制</text>
          <view class="setting-control">
            <input 
              class="setting-input" 
              type="number" 
              v-model.number="dailyLimitCigarettes"
              @blur="saveDailyLimit"
            />
            <text class="setting-unit">根/天</text>
          </view>
        </view>
        
        <view class="setting-item">
          <text class="setting-label">电子烟限制</text>
          <view class="setting-control">
            <input 
              class="setting-input" 
              type="number" 
              v-model.number="dailyLimitPuffs"
              @blur="saveDailyLimit"
            />
            <text class="setting-unit">口/天</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 关于应用 -->
    <view class="settings-section card">
      <view class="section-header">
        <text class="section-title">关于</text>
      </view>
      
      <view class="setting-item" @click="showAbout">
        <text class="setting-label">关于轻清戒烟日记</text>
        <text class="setting-arrow">></text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">版本号</text>
        <text class="setting-value">v1.0.0</text>
      </view>
    </view>

    <!-- 操作（放置在页面末尾） -->
    <view class="settings-section card">
      <view class="section-header">
        <text class="section-title">操作</text>
      </view>
      <view class="actions-row">
        <button class="btn btn--sm" @click="showResetModal">恢复默认设置</button>
        <button class="btn btn--sm btn--danger" @click="showLogoutModal">退出登录</button>
      </view>
    </view>

    
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserSettings } from '../../composables/useUserSettings.js'
import { useTheme } from '../../composables/useTheme.js'

// 状态管理
const { 
  userSettings,
  initializeUserData, 
  
  setMaxActiveBoxes,
  setReminderEnabled,
  setDailyLimit,
  setDefaultSpecs,
  resetSettings,
  logout
} = useUserSettings()

// 主题
const { currentTheme, setTheme } = useTheme()

// 本地状态
const dailyLimitCigarettes = ref(10)
const dailyLimitPuffs = ref(100)

// 页面初始化
onMounted(() => {
  initializeUserData()
  
  // 初始化每日限制数据
  if (userSettings.value.dailyLimit) {
    dailyLimitCigarettes.value = userSettings.value.dailyLimit.cigarettes || 10
    dailyLimitPuffs.value = userSettings.value.dailyLimit.puffs || 100
  }
})

// 方法定义
const decreaseMaxBoxes = () => {
  if (userSettings.value.maxActiveBoxes > 1) {
    setMaxActiveBoxes(userSettings.value.maxActiveBoxes - 1)
  }
}

const increaseMaxBoxes = () => {
  if (userSettings.value.maxActiveBoxes < 10) {
    setMaxActiveBoxes(userSettings.value.maxActiveBoxes + 1)
  }
}

const onReminderChange = (e) => {
  setReminderEnabled(e.detail.value)
}

const onDailyLimitToggle = (e) => {
  const enabled = e.detail.value
  
  if (enabled) {
    setDailyLimit({
      enabled: true,
      cigarettes: dailyLimitCigarettes.value,
      puffs: dailyLimitPuffs.value
    })
  } else {
    setDailyLimit({ enabled: false })
  }
}

const saveDailyLimit = () => {
  if (userSettings.value.dailyLimit?.enabled) {
    setDailyLimit({
      enabled: true,
      cigarettes: dailyLimitCigarettes.value,
      puffs: dailyLimitPuffs.value
    })
  }
}

const saveSettings = () => {
  // 保存默认规格设置（即时写库）
  setDefaultSpecs({
    cigaretteCount: userSettings.value.defaultCigaretteCount,
    electronicPuffs: userSettings.value.defaultElectronicPuffs
  })
}

const showResetModal = () => {
  uni.showModal({
    title: '恢复默认设置',
    content: '将恢复为应用默认设置，是否继续？',
    confirmText: '恢复',
    confirmColor: '#2C5282',
    success: (res) => {
      if (res.confirm) {
        resetSettings()
        uni.showToast({ title: '已恢复默认设置', icon: 'success' })
      }
    }
  })
}

const showLogoutModal = () => {
  uni.showModal({
    title: '退出登录',
    content: '确定要退出当前账号吗？退出后需要重新登录。',
    confirmText: '退出',
    confirmColor: '#E53E3E',
    success: (res) => {
      if (res.confirm) {
        handleLogout()
      }
    }
  })
}

const handleLogout = () => {
  try {
    const result = logout()
    uni.showToast({ title: '已退出登录', icon: 'success' })
    setTimeout(() => {
      uni.reLaunch({ url: '/pages/index/index' })
    }, 1500)
  } catch (error) {
    uni.showToast({ title: '退出失败', icon: 'none' })
  }
}

// 即时切换主题
const changeTheme = (theme) => {
  setTheme(theme)
  uni.showToast({ title: '主题已切换', icon: 'success' })
}

// 关于弹窗（沿用个人页内容）
const showAbout = () => {
  uni.showModal({
    title: '关于轻清戒烟日记',
    content: '这是一个帮助记录和管理抽烟习惯的工具应用。\n\n版本：v1.0.0\n开发者：轻清戒烟日记团队',
    showCancel: false
  })
}
</script>

<style lang="scss" scoped>
.settings-page {
  padding: $app-spacing-md;
  min-height: 100vh;
  background-color: var(--color-bg-page);
}

.settings-section {
  padding: $app-spacing-md;
  margin-bottom: $app-spacing-md;
}

.section-header {
  margin-bottom: $app-spacing-md;
}

.section-title {
  font-size: $app-font-size-base;
  font-weight: bold;
  color: var(--color-text-primary);
  margin-bottom: $app-spacing-xs;
}

.section-desc {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $app-spacing-sm 0;
  border-bottom: 1px solid var(--color-border);
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: var(--color-bg-section);
  }
}

.setting-label {
  font-size: $app-font-size-base;
  color: var(--color-text-primary);
  flex: 1;
  
  &.danger {
    color: var(--color-danger);
  }
}

.setting-desc {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
  margin-left: $app-spacing-md;
}

.setting-arrow {
  font-size: $app-font-size-base;
  color: var(--color-text-muted);
  margin-left: $app-spacing-sm;
}

.setting-control {
  display: flex;
  align-items: center;
  gap: $app-spacing-xs;
}

.btn-amount {
  background-color: var(--color-border);
  color: var(--color-text-primary);
  border: none;
  border-radius: $app-button-radius;
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $app-font-size-base;
}

.amount-display {
  font-size: $app-font-size-base;
  font-weight: bold;
  color: var(--color-text-primary);
  min-width: 80rpx;
  text-align: center;
}

.setting-input {
  padding: $app-spacing-xs $app-spacing-xs;
  border: 1px solid var(--color-border);
  border-radius: $app-input-radius;
  font-size: $app-font-size-sm;
  text-align: center;
  width: 140rpx;
  color: var(--color-text-primary);
}
.setting-input::placeholder { color: var(--color-text-muted); }

.setting-unit {
  font-size: $app-font-size-sm;
  color: var(--color-text-muted);
}

.setting-value {
  font-size: $app-font-size-sm;
  color: var(--color-text-secondary);
}

/* 操作区 */
.actions-row {
  display: flex;
  gap: $app-spacing-sm;
}

.actions-row .btn {
  flex: 1;
}

.btn--danger {
  background-color: var(--color-danger);
  color: var(--color-text-white);
}
</style>
