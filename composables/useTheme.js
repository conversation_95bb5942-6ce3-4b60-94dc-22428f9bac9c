import { computed } from 'vue'
import { useUserSettings } from './useUserSettings.js'

/**
 * 主题组合式
 * 将用户设置的 theme 值映射为页面可绑定的类名：'theme-light' | 'theme-dark'
 */
export const useTheme = () => {
  const { userSettings, setTheme } = useUserSettings()

  const currentTheme = computed(() => {
    const t = userSettings.value?.theme
    return t === 'dark' ? 'theme-dark' : 'theme-light'
  })

  return {
    currentTheme,
    setTheme
  }
}


