/**
 * 图表数据构建（周/月/年）
 */

import { computed } from 'vue'
import dayjs from '../utils/dayjs-setup.js'
import { formatDateOnly, enumerateDays, getWeekRangeByOffset, getMonthRangeByOffset, getYearRangeByOffset } from '../utils/dateUtils.js'

/**
 * @param {Ref} consumptionRecords
 * @param {Ref} activeBoxes
 * @param {Function} getBrand
 */
export const useChartsData = (consumptionRecords, activeBoxes, getBrand) => {

  const aggregateByDay = (start, end) => {
    const days = enumerateDays(start, end) // YYYY/MM/DD
    const xLabels = days.map(d => dayjs(d, 'YYYY/MM/DD').format('MM/DD'))
    const cig = new Array(days.length).fill(0)
    const puff = new Array(days.length).fill(0)
    const cigFake = new Array(days.length).fill(0)
    const puffFake = new Array(days.length).fill(0)

    const records = consumptionRecords.value || []
    for (const r of records) {
      const d = formatDateOnly(r.timestamp) // YYYY/MM/DD
      const idx = days.indexOf(d)
      if (idx === -1) continue
      if (r.fake) {
        if (r.type === 'cigarette') cigFake[idx] += r.amount || 0
        else puffFake[idx] += r.amount || 0
      } else {
        if (r.type === 'cigarette') cig[idx] += r.amount || 0
        else puff[idx] += r.amount || 0
      }
    }

    return { categories: xLabels, cig, puff, cigFake, puffFake }
  }

  const aggregateByMonth = (start, end) => {
    // enumerateDays + group by month index
    const days = enumerateDays(start, end)
    const endDay = dayjs(end, 'YYYY/MM/DD')
    const year = endDay.year()
    const monthlyCig = new Array(12).fill(0)
    const monthlyPuff = new Array(12).fill(0)
    const monthlyCigFake = new Array(12).fill(0)
    const monthlyPuffFake = new Array(12).fill(0)

    const records = consumptionRecords.value || []
    for (const r of records) {
      const d = dayjs(formatDateOnly(r.timestamp), 'YYYY/MM/DD')
      if (!d.isValid()) continue
      if (d.year() !== year) continue
      const m = d.month() // 0..11
      if (r.fake) {
        if (r.type === 'cigarette') monthlyCigFake[m] += r.amount || 0
        else monthlyPuffFake[m] += r.amount || 0
      } else {
        if (r.type === 'cigarette') monthlyCig[m] += r.amount || 0
        else monthlyPuff[m] += r.amount || 0
      }
    }
    const categories = new Array(12).fill(0).map((_, i) => `${i + 1}月`)
    return { categories, cig: monthlyCig, puff: monthlyPuff, cigFake: monthlyCigFake, puffFake: monthlyPuffFake }
  }

  const buildBoxTotals = (start, end) => {
    const map = new Map()
    const records = consumptionRecords.value || []
    for (const r of records) {
      const d = formatDateOnly(r.timestamp)
      if (d < start || d > end) continue
      // 云吸记录按类型拆分归组，避免被合并
      const isVirtual = !!r.fake && r.boxId === '__VIRTUAL__'
      const groupKey = isVirtual ? `__VIRTUAL__:${r.type}` : r.boxId
      const cur = map.get(groupKey) || { boxId: groupKey, cigarettes: 0, puffs: 0, total: 0, type: r.type, virtual: isVirtual }
      if (r.type === 'cigarette') cur.cigarettes += r.amount || 0
      else cur.puffs += r.amount || 0
      cur.total = cur.cigarettes + cur.puffs
      map.set(groupKey, cur)
    }
    const list = Array.from(map.values())
      .sort((a, b) => (b.total || 0) - (a.total || 0))
      .map(item => {
        if (item.virtual) {
          // 构造虚拟品牌占位信息
          const brand = item.type === 'cigarette'
            ? { id: 'VIRTUAL_CIG', name: '轻清云卷烟', type: 'cigarette', virtual: true }
            : { id: 'VIRTUAL_ELEC', name: '轻清云电子烟', type: 'electronic', virtual: true }
          return { ...item, box: null, brand }
        }
        const realBoxId = item.boxId
        const box = (activeBoxes.value || []).find(b => b.id === realBoxId) || null
        const brand = box ? getBrand(box.brandId) : null
        return { ...item, box, brand }
      })
    return list
  }

  const summarizeBoxCountsByType = (boxList) => {
    const list = Array.isArray(boxList) ? boxList : []
    const cigarette = list.filter(it => (it.box && it.box.type ? it.box.type === 'cigarette' : it.type === 'cigarette') && (it.cigarettes || 0) > 0).length
    const electronic = list.filter(it => (it.box && it.box.type ? it.box.type === 'electronic' : it.type === 'electronic') && (it.puffs || 0) > 0).length
    return { cigarette, electronic }
  }

  const summarizeTotals = (cigArr, puffArr) => {
    const cigarettes = (cigArr || []).reduce((s, v) => s + (v || 0), 0)
    const puffs = (puffArr || []).reduce((s, v) => s + (v || 0), 0)
    return { cigarettes, puffs, records: cigarettes + puffs }
  }

  const buildWeeklySeries = (offset = 0) => {
    const { start, end, label } = getWeekRangeByOffset(offset)
    const agg = aggregateByDay(start, end)
    const totals = summarizeTotals(agg.cig, agg.puff)
    const categories = agg.categories
    const series = [
      { name: '卷烟(根)', data: agg.cig },
      { name: '电子(口)', data: agg.puff },
      { name: '云卷烟(根)', data: agg.cigFake },
      { name: '云电子(口)', data: agg.puffFake }
    ]
    const boxes = buildBoxTotals(start, end)
    const boxCounts = summarizeBoxCountsByType(boxes)
    return { categories, series, totals, range: { start, end, label }, boxes, boxCounts }
  }

  const buildMonthlySeries = (offset = 0) => {
    const { start, end, label } = getMonthRangeByOffset(offset)
    const agg = aggregateByDay(start, end)
    const totals = summarizeTotals(agg.cig, agg.puff)
    const series = [
      { name: '卷烟(根)', data: agg.cig },
      { name: '电子(口)', data: agg.puff },
      { name: '云卷烟(根)', data: agg.cigFake },
      { name: '云电子(口)', data: agg.puffFake }
    ]
    const boxes = buildBoxTotals(start, end)
    const boxCounts = summarizeBoxCountsByType(boxes)
    return { categories: agg.categories, series, totals, range: { start, end, label }, boxes, boxCounts }
  }

  const buildYearlySeries = (offset = 0) => {
    const { start, end, label, year } = getYearRangeByOffset(offset)
    const agg = aggregateByMonth(start, end)
    const totals = summarizeTotals(agg.cig, agg.puff)
    const series = [
      { name: '卷烟(根)', data: agg.cig },
      { name: '电子(口)', data: agg.puff },
      { name: '云卷烟(根)', data: agg.cigFake },
      { name: '云电子(口)', data: agg.puffFake }
    ]
    const boxes = buildBoxTotals(start, end)
    const boxCounts = summarizeBoxCountsByType(boxes)
    return { categories: agg.categories, series, totals, range: { start, end, label, year }, boxes, boxCounts }
  }

  return {
    buildWeeklySeries,
    buildMonthlySeries,
    buildYearlySeries
  }
}


