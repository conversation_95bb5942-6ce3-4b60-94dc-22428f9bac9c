/**
 * Achievements evaluation and sync
 */

import { ref, computed } from 'vue'
import dayjs from '../utils/dayjs-setup.js'
import { formatDateOnly, getToday } from '../utils/dateUtils.js'
import achievementsApi from '../utils/achievements.js'
import { useUserSettings } from './useUserSettings.js'

const achievementsState = ref({ unlockedMap: {}, unreadCount: 0, progressMap: {} })

// Catalog definition
const catalog = [
  // Positive: streak
  ...[1,3,7,21,30,50,100,200,365].map(n => ({ id: `streak_${n}`, group: 'positive', name: `连续${n}天`, desc: `连续${n}天有记录`, color: 'positive', condition: 'streak', threshold: n })),
  // Positive: active days
  ...[30,100,365,500,800,1000].map(n => ({ id: `days_${n}`, group: 'positive', name: `累计${n}天`, desc: `累计${n}天有记录`, color: 'positive', condition: 'days_total', threshold: n })),
  // Positive: safe streak
  ...[3,7,21,30].map(n => ({ id: `safe_${n}`, group: 'positive', name: `连续${n}天未超限`, desc: `有记录且未超过每日上限`, color: 'positive', condition: 'safe_streak', threshold: n })),
  // Positive: month all safe
  { id: 'month_all_safe', group: 'positive', name: '本月零超限', desc: '本月所有记录日均未超限', color: 'positive', condition: 'month_all_safe', threshold: 1 },
  // Positive: brand diversity
  ...[5,10,20,30].map(n => ({ id: `brand_diversity_${n}`, group: 'positive', name: `尝试${n}个品牌`, desc: '不同品牌达到阈值', color: 'positive', condition: 'brand_diversity', threshold: n })),
  // Positive: spent milestones
  ...[100,500,1000,5000,10000,20000,30000,50000].map(n => ({ id: `spent_${n}`, group: 'milestone', name: `累计消费≥${n}元`, desc: '记录坚持，注意理性消费', color: 'milestone', condition: 'spent_total', threshold: n })),
  // Negative: over days
  ...[1,3,7,30,90].map(n => ({ id: `over_${n}`, group: 'negative', name: `累计超限${n}天`, desc: '超过每日上限的天数', color: 'negative', condition: 'over_days', threshold: n })),
  // Negative: heavy over day (2x,3x)
  { id: 'day_over_2x', group: 'negative', name: '单日严重超限x2', desc: '单日超过上限2倍', color: 'negative', condition: 'heavy_over', threshold: 2 },
  { id: 'day_over_3x', group: 'negative', name: '单日极限超限x3', desc: '单日超过上限3倍', color: 'negative', condition: 'heavy_over', threshold: 3 },
  // Negative: over streak
  ...[3,7,30,90].map(n => ({ id: `over_streak_${n}`, group: 'negative', name: `连续${n}天超限`, desc: '连续记录日均超限', color: 'negative', condition: 'over_streak', threshold: n })),
  // Milestones
  { id: 'first_record', group: 'milestone', name: '第一条记录', desc: '开始记录的第一天', color: 'milestone', condition: 'first_record', threshold: 1 },
  { id: 'first_custom_brand', group: 'milestone', name: '自定义品牌', desc: '首次添加自定义品牌', color: 'milestone', condition: 'first_custom_brand', threshold: 1 },
  { id: 'first_box_finished', group: 'milestone', name: '完成一盒', desc: '首次完成一盒/烟弹', color: 'milestone', condition: 'first_box_finished', threshold: 1 },
  { id: 'first_cloud_breath', group: 'milestone', name: '首次云吸', desc: '第一次云吸记录', color: 'milestone', condition: 'first_cloud', threshold: 1 },
  { id: 'cloud_10', group: 'milestone', name: '十次云吸', desc: '累计云吸次数≥10', color: 'milestone', condition: 'cloud_total', threshold: 10 },
  { id: 'cloud_100', group: 'milestone', name: '一百次云吸', desc: '累计云吸次数≥100', color: 'milestone', condition: 'cloud_total', threshold: 100 },
  { id: 'records_10', group: 'milestone', name: '十次记录', desc: '累计记录次数≥10', color: 'milestone', condition: 'records_total', threshold: 10 },
  { id: 'records_50', group: 'milestone', name: '五十次记录', desc: '累计记录次数≥50', color: 'milestone', condition: 'records_total', threshold: 50 },
  { id: 'records_100', group: 'milestone', name: '百次记录', desc: '累计记录次数≥100', color: 'milestone', condition: 'records_total', threshold: 100 },
  { id: 'records_500', group: 'milestone', name: '五百次记录', desc: '累计记录次数≥500', color: 'milestone', condition: 'records_total', threshold: 500 },
  { id: 'records_1000', group: 'milestone', name: '一千次记录', desc: '累计记录次数≥1000', color: 'milestone', condition: 'records_total', threshold: 1000 }
]

export const useAchievements = (consumptionRecords, activeBoxes, getBrand, overallStatsProvider) => {
  const { isDailyLimitEnabled, checkDailyLimit } = useUserSettings()

  function buildDayMap() {
    const map = new Map()
    for (const r of (consumptionRecords.value || [])) {
      const d = formatDateOnly(r.timestamp)
      const prev = map.get(d) || { c: 0, p: 0 }
      if (r.type === 'cigarette') prev.c += r.amount; else prev.p += r.amount
      map.set(d, prev)
    }
    // add exceeded
    for (const [d, v] of map.entries()) {
      const chk = checkDailyLimit(v.c, v.p)
      v.exceeded = !!chk.exceeded
    }
    return map
  }

  function calcConsecutive(map, predicate) {
    let count = 0
    let cursor = dayjs(getToday(), 'YYYY-MM-DD')
    // 以有记录的日子为连续定义：遇到“无记录”即中断
    while (true) {
      const key = cursor.format('YYYY/MM/DD')
      const info = map.get(key)
      if (!info) break
      if (predicate(info)) count += 1; else break
      cursor = cursor.subtract(1, 'day')
      if (count > 2000) break
    }
    return count
  }

  function calcBrandDiversity() {
    const used = new Set()
    for (const r of (consumptionRecords.value || [])) {
      const box = activeBoxes.value.find(b => b.id === r.boxId)
      if (box) used.add(box.brandId)
    }
    return used.size
  }

  function calcRecordsCount() { return (consumptionRecords.value || []).length }

  function calcCloudBreathsCount() {
    let n = 0
    for (const r of (consumptionRecords.value || [])) {
      if (r && r.fake) n += 1
    }
    return n
  }

  function calcOverDays(map) {
    let n = 0
    for (const info of map.values()) { if (info.exceeded) n++ }
    return n
  }

  function calcHeavyOver(map, multiple) {
    if (!isDailyLimitEnabled.value) return false
    let ok = false
    for (const [d, v] of map.entries()) {
      const chk = checkDailyLimit(v.c, v.p)
      if (!chk.exceeded) continue
      const limit = chk.type === 'cigarettes' ? chk.limit : chk.limit
      const cur = chk.type === 'cigarettes' ? v.c : v.p
      if (limit && cur >= limit * multiple) { ok = true; break }
    }
    return ok
  }

  function calcOverStreak(map) {
    return calcConsecutive(map, info => info.exceeded)
  }

  function calcSafeStreak(map) {
    return calcConsecutive(map, info => !info.exceeded)
  }

  function calcStreak(map) {
    return calcConsecutive(map, _ => true)
  }

  function monthAllSafe(map) {
    if (!isDailyLimitEnabled.value) return false
    const now = dayjs()
    const start = now.startOf('month')
    const end = now.endOf('month')
    let hasRecord = false
    for (let d = start; d.isBefore(end) || d.isSame(end, 'day'); d = d.add(1, 'day')) {
      const key = d.format('YYYY/MM/DD')
      const info = map.get(key)
      if (!info) continue
      hasRecord = true
      if (info.exceeded) return false
    }
    return hasRecord
  }

  function evaluateAll() {
    const map = buildDayMap()
    const unlocked = []
    const progress = {}
    const streak = calcStreak(map)
    const safeStreak = calcSafeStreak(map)
    const overStk = calcOverStreak(map)
    const overDays = calcOverDays(map)
    const brandDiv = calcBrandDiversity()
    const recCount = calcRecordsCount()
    const cloudCount = calcCloudBreathsCount()
    const spent = (overallStatsProvider?.value?.totalSpent) || 0
    const totalDays = (overallStatsProvider?.value?.totalDays) || map.size

    for (const a of catalog) {
      let ok = false
      switch (a.condition) {
        case 'streak': ok = streak >= a.threshold; progress[a.id] = { current: streak, target: a.threshold }; break
        case 'days_total': ok = totalDays >= a.threshold; progress[a.id] = { current: totalDays, target: a.threshold }; break
        case 'safe_streak': ok = isDailyLimitEnabled.value && safeStreak >= a.threshold; progress[a.id] = { current: safeStreak, target: a.threshold }; break
        case 'month_all_safe': ok = monthAllSafe(map); break
        case 'brand_diversity': ok = brandDiv >= a.threshold; progress[a.id] = { current: brandDiv, target: a.threshold }; break
        case 'spent_total': ok = spent >= a.threshold; progress[a.id] = { current: Math.round(spent), target: a.threshold }; break
        case 'over_days': ok = isDailyLimitEnabled.value && overDays >= a.threshold; progress[a.id] = { current: overDays, target: a.threshold }; break
        case 'heavy_over': ok = calcHeavyOver(map, a.threshold); break
        case 'over_streak': ok = isDailyLimitEnabled.value && overStk >= a.threshold; progress[a.id] = { current: overStk, target: a.threshold }; break
        case 'first_record': ok = recCount >= 1; progress[a.id] = { current: recCount, target: 1 }; break
        case 'first_custom_brand': ok = false; break // 暂由页面触发时补充（品牌创建成功后可显式同步）
        case 'first_box_finished': ok = false; break // 完成盒子时由业务触发
        case 'first_cloud': ok = (consumptionRecords.value || []).some(r => r.fake); break
        case 'cloud_total': ok = cloudCount >= a.threshold; progress[a.id] = { current: cloudCount, target: a.threshold }; break
        case 'records_total': ok = recCount >= a.threshold; progress[a.id] = { current: recCount, target: a.threshold }; break
        default: ok = false
      }
      if (ok) unlocked.push({ id: a.id, unlockedAt: Date.now() })
    }
    achievementsState.value.progressMap = progress
    return { unlocked, progress }
  }

  async function loadFromCloud() {
    try {
      const res = await achievementsApi.getUserAchievements()
      if (res.success) {
        const map = {}
        for (const a of (res.data.achievements || [])) map[a.id] = a
        achievementsState.value.unlockedMap = map
        achievementsState.value.unreadCount = res.data.unread || 0
      }
    } catch (_) {}
  }

  async function syncUnlocks(list) {
    if (!list || list.length === 0) return { newlyAddedIds: [], unread: achievementsState.value.unreadCount }
    const res = await achievementsApi.syncUnlocked({ unlocked: list })
    if (res.success) {
      const map = {}
      for (const a of (res.data.achievements || [])) map[a.id] = a
      achievementsState.value.unlockedMap = map
      achievementsState.value.unreadCount = res.data.unread || 0
      return { newlyAddedIds: res.data.newlyAddedIds || [], unread: achievementsState.value.unreadCount }
    }
    return { newlyAddedIds: [], unread: achievementsState.value.unreadCount }
  }

  async function markAllRead() {
    const ids = Object.keys(achievementsState.value.unlockedMap)
    const res = await achievementsApi.markRead(ids)
    if (res.success) achievementsState.value.unreadCount = res.data.unread || 0
  }

  // 综合流程：计算 → 去除已解锁 → 同步
  async function evaluateAndSync() {
    const { unlocked } = evaluateAll()
    const map = achievementsState.value.unlockedMap || {}
    const delta = unlocked.filter(u => !map[u.id])
    return await syncUnlocks(delta)
  }

  // 显式同步一批成就ID（用于事件触发型）
  async function unlockExplicit(ids) {
    if (!ids || !ids.length) return { newlyAddedIds: [] }
    const list = ids.map(id => ({ id, unlockedAt: Date.now() }))
    return await syncUnlocks(list)
  }

  return {
    catalog,
    achievementsState: computed(() => achievementsState.value),
    evaluateAll,
    evaluateAndSync,
    loadFromCloud,
    markAllRead,
    unlockExplicit
  }
}


