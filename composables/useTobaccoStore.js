/**
 * 烟草数据状态管理
 * 使用Vue3 Composition API管理烟草相关的所有数据
 */

import { ref, computed } from 'vue'
import { parseToDayjs } from '../utils/dateUtils.js'
import { getToday, formatDateOnly } from '../utils/dateUtils.js'
import smokingApi from '../utils/smoking.js'
import { useUserSettings } from './useUserSettings.js'

// 响应式数据状态
const activeBoxes = ref([])
const consumptionRecords = ref([])
const brands = ref([])
// 不再做历史缓存控制，按需请求

/**
 * 烟草数据存储组合函数
 */
export const useTobaccoStore = () => {
  const { refreshUserAggregate } = useUserSettings()
  
  /**
   * 初始化数据
   */
  const initializeData = async () => {
    try {
      const [boxesRes, brandsRes] = await Promise.all([
        smokingApi.listBoxes({ activeOnly: false }),
        smokingApi.getBrands({ scope: 'mine' })
      ])
      activeBoxes.value = boxesRes.success ? (boxesRes.data || []) : []
      brands.value = brandsRes.success ? (brandsRes.data || []) : []
    } catch (e) {
      console.error('initializeData failed:', e)
      activeBoxes.value = []
      consumptionRecords.value = []
      brands.value = []
    }
  }
  
  
  
  /**
   * 添加新的烟盒
   * @param {object} boxData 烟盒数据
   */
  const addTobaccoBox = async (boxData) => {
    const res = await smokingApi.createBox({
      brandId: boxData.brandId,
      type: boxData.type,
      price: boxData.price,
      nickname: boxData.nickname || '',
      specs: boxData.specs
    })
    if (res.success) {
      activeBoxes.value.unshift(res.data)
      return res.data
    }
    throw new Error(res.message || '创建烟盒失败')
  }
  
  /**
   * 更新烟盒信息
   * @param {string} boxId 烟盒ID
   * @param {object} updateData 更新的数据
   */
  const updateTobaccoBox = async (boxId, updateData) => {
    const res = await smokingApi.updateBox(boxId, updateData)
    if (res.success) {
      const index = activeBoxes.value.findIndex(box => box.id === boxId)
      if (index !== -1) {
        activeBoxes.value[index] = { ...activeBoxes.value[index], ...updateData }
        return activeBoxes.value[index]
      }
    }
    return null
  }
  
  /**
   * 删除烟盒
   * @param {string} boxId 烟盒ID
   */
  const removeTobaccoBox = async (boxId) => {
    const res = await smokingApi.removeBox(boxId)
    if (res.success) {
      const index = activeBoxes.value.findIndex(box => box.id === boxId)
      if (index !== -1) {
        activeBoxes.value.splice(index, 1)
      }
      return true
    }
    return false
  }
  
  /**
   * 完成烟盒（标记为已抽完）
   * @param {string} boxId 烟盒ID
   */
  const completeTobaccoBox = async (boxId) => {
    const res = await smokingApi.completeBox(boxId)
    if (res.success) {
      // 卷烟完成时，后端会返回一条计费记录（amount=0, spent=box.price）
      if (res.data && res.data.recordId) {
        const rec = { id: res.data.recordId, boxId, timestamp: res.data.timestamp, amount: 0, type: 'cigarette', note: '' }
        if (typeof res.data.spent === 'number') rec.spent = res.data.spent
        consumptionRecords.value.unshift(rec)
      }
      // 刷新盒子状态
      const boxes = await smokingApi.listBoxes({ activeOnly: false })
      if (boxes.success) activeBoxes.value = boxes.data || []
      await refreshUserAggregate()
      return updateTobaccoBox(boxId, { isCompleted: true })
    }
    return null
  }
  
  /**
   * 完成当前电子烟烟弹（仅最后一颗整盒计费；否则不计费，推进到下一颗）
   * @param {string} boxId 烟盒ID
   */
  const completeCurrentCartridge = async (boxId) => {
    const res = await smokingApi.completeCartridge(boxId)
    if (res.success) {
      // 插入一条完成记录（amount=0，只有最后一颗时 spent=整盒价，否则为0）
      const rec = { id: res.data.recordId, boxId, timestamp: res.data.timestamp, amount: 0, type: 'electronic', note: '' }
      if (typeof res.data.spent === 'number') rec.spent = res.data.spent
      consumptionRecords.value.unshift(rec)
      // 刷新盒子状态
      const boxes = await smokingApi.listBoxes({ activeOnly: false })
      if (boxes.success) activeBoxes.value = boxes.data || []
      await refreshUserAggregate()
      return { ok: true, boxCompleted: !!res.data.boxCompleted }
    }
    return { ok: false }
  }
  
  /**
   * 添加消费记录
   * @param {object} recordData 消费记录数据
   */
  const addConsumptionRecord = async (recordData) => {
    const res = await smokingApi.addRecord({ boxId: recordData.boxId, type: recordData.type, amount: recordData.amount, note: recordData.note || '' })
    if (res.success) {
      // 使用后端返回的实际数量（卷烟会被截断不超过预设）
      const rec = { id: res.data.recordId, boxId: recordData.boxId, timestamp: res.data.timestamp, amount: (typeof res.data.amount === 'number' ? res.data.amount : recordData.amount), type: recordData.type, note: recordData.note || '' }
      if (typeof res.data.spent === 'number') rec.spent = res.data.spent
      consumptionRecords.value.unshift(rec)
      // 如果后端告知该盒完成，追加一条完成计费记录
      if (res.data && res.data.completed && res.data.completionRecord) {
        const cr = res.data.completionRecord
        const finishRec = { id: cr.recordId, boxId: recordData.boxId, timestamp: cr.timestamp, amount: 0, type: recordData.type, note: '' }
        if (typeof cr.spent === 'number') finishRec.spent = cr.spent
        consumptionRecords.value.unshift(finishRec)
      }
      // 刷新盒子状态（简单方式：从云端拉取最新盒子列表，避免本地计算误差）
      const boxes = await smokingApi.listBoxes({ activeOnly: false })
      if (boxes.success) activeBoxes.value = boxes.data || []
      await refreshUserAggregate()
      return rec
    }
    throw new Error(res.message || '记录失败')
  }

  /**
   * 添加云吸虚拟记录（不影响盒子与费用）
   * @param {{ type: 'cigarette'|'electronic', amount: number, note?: string }} payload
   */
  const addVirtualConsumptionRecord = async (payload) => {
    const res = await smokingApi.addVirtualRecord({ type: payload.type, amount: payload.amount, note: payload.note || '' })
    if (res.success) {
      const rec = { id: res.data.recordId, boxId: '__VIRTUAL__', timestamp: res.data.timestamp, amount: payload.amount, type: payload.type, note: payload.note || '', spent: 0, fake: true }
      consumptionRecords.value.unshift(rec)
      return rec
    }
    throw new Error(res.message || '记录失败')
  }

  /**
   * 获取最近一次同类型云吸时间戳
   * @param {'cigarette'|'electronic'} type
   * @returns {number} 毫秒时间戳，若无则0
   */
  const getLastFakeTimestamp = (type) => {
    const list = consumptionRecords.value || []
    for (const r of list) {
      if (r && r.fake && r.type === type) return r.timestamp || 0
    }
    return 0
  }
  
  /**
   * 更新消费记录
   * @param {string} recordId 记录ID
   * @param {object} updateData 更新的数据
   */
  const updateConsumptionRecord = async (recordId, updateData) => {
    const res = await smokingApi.updateRecord(recordId, updateData)
    if (res.success) {
      const index = consumptionRecords.value.findIndex(record => record.id === recordId)
      if (index !== -1) {
        consumptionRecords.value[index] = { ...consumptionRecords.value[index], ...updateData }
        // 刷新盒子状态
        const boxes = await smokingApi.listBoxes({ activeOnly: false })
        if (boxes.success) activeBoxes.value = boxes.data || []
        await refreshUserAggregate()
        return consumptionRecords.value[index]
      }
    }
    return null
  }
  
  /**
   * 删除消费记录
   * @param {string} recordId 记录ID
   */
  const removeConsumptionRecord = async (recordId) => {
    // 与“删除”语义一致地调用后端撤销（无软删除）
    const ok = await undoConsumptionRecord(recordId)
    return !!ok
  }

  /**
   * 撤销一条消费记录：删除记录并回滚对应烟盒的进度
   * @param {string} recordId 记录ID
   * @returns {boolean} 是否成功
   */
  const undoConsumptionRecord = async (recordId) => {
    const res = await smokingApi.undoRecord(recordId)
    if (res.success) {
      // 移除本地记录
      const index = consumptionRecords.value.findIndex(r => r.id === recordId)
      if (index !== -1) consumptionRecords.value.splice(index, 1)
      // 刷新盒子状态
      const boxes = await smokingApi.listBoxes({ activeOnly: false })
      if (boxes.success) activeBoxes.value = boxes.data || []
      await refreshUserAggregate()
      return true
    }
    return false
  }
  
  // 计算属性
  const activeBoxesCount = computed(() => activeBoxes.value.filter(box => !box.isCompleted).length)
  
  const todayRecords = computed(() => {
    const today = getToday()
    return consumptionRecords.value.filter(record => {
      const dayStr = formatDateOnly(record.timestamp)
      return dayStr === today
    })
  })
  
  const activeBoxesSorted = computed(() => {
    const toTs = (val) => {
      if (typeof val === 'number') return val
      const d = parseToDayjs(val)
      return d.isValid() ? d.valueOf() : 0
    }
    return activeBoxes.value
      .filter(box => !box.isCompleted)
      .sort((a, b) => toTs(b.lastUsedDate) - toTs(a.lastUsedDate))
  })
  
  /**
   * 根据ID获取烟盒信息
   * @param {string} boxId 烟盒ID
   */
  const getBoxById = (boxId) => {
    return activeBoxes.value.find(box => box.id === boxId)
  }
  
  /**
   * 根据ID获取品牌信息
   * @param {string} brandId 品牌ID
   */
  const getBrand = (brandId) => {
    const list = brands.value || []
    return list.find(b => b.id === brandId) || null
  }
  
  /**
   * 获取烟盒的详细信息（包含品牌信息）
   * @param {string} boxId 烟盒ID
   */
  const getBoxWithBrand = (boxId) => {
    const box = getBoxById(boxId)
    if (!box) return null
    
    const brand = getBrand(box.brandId)
    return {
      ...box,
      brand
    }
  }
  
  return {
    // 数据状态
    activeBoxes,
    consumptionRecords,
    brands,
    
    // 计算属性
    activeBoxesCount,
    todayRecords,
    activeBoxesSorted,
    
    // 方法
    initializeData,
    fetchRecords,
    setConsumptionRecords,
    loadRecentDays,
    addTobaccoBox,
    updateTobaccoBox,
    removeTobaccoBox,
    completeTobaccoBox,
    completeCurrentCartridge,
    addConsumptionRecord,
    updateConsumptionRecord,
    removeConsumptionRecord,
    undoConsumptionRecord,
    addVirtualConsumptionRecord,
    getLastFakeTimestamp,
    getBoxById,
    getBrand,
    getBoxWithBrand
  }
}

/**
 * 直接按日期范围从云端拉取记录（不合并全局，返回数组）
 * @param {string} startISO YYYY-MM-DD
 * @param {string} endISO YYYY-MM-DD
 * @returns {Promise<Array<any>>}
 */
async function fetchRecords(startISO, endISO) {
  try {
    if (!startISO || !endISO) return []
    const res = await smokingApi.listRecords({ startDate: startISO, endDate: endISO })
    if (!res.success) return []
    const list = Array.isArray(res.data) ? res.data : []
    // 统一排序（新->旧）
    return list.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0))
  } catch (e) {
    console.error('fetchRecords failed:', e)
    return []
  }
}

/**
 * 写入全局消费记录（首页统计使用）
 * @param {Array<any>} records 
 * @param {'replace'|'append'} mode 
 */
function setConsumptionRecords(records, mode = 'replace') {
  const incoming = Array.isArray(records) ? records : []
  if (mode === 'append') {
    const map = new Map()
    for (const r of consumptionRecords.value) map.set(r.id, r)
    for (const r of incoming) map.set(r.id, r)
    const merged = Array.from(map.values())
    merged.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0))
    consumptionRecords.value = merged
  } else {
    consumptionRecords.value = incoming.slice().sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0))
  }
}

/**
 * 拉取最近 N 天记录并写入全局（首页“今日/昨日/较昨日”使用）
 * @param {number} days 
 */
async function loadRecentDays(days = 8) {
  try {
    const end = new Date()
    const start = new Date(end.getTime() - (Math.max(1, days) - 1) * 24 * 60 * 60 * 1000)
    const startISO = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2,'0')}-${String(start.getDate()).padStart(2,'0')}`
    const endISO = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2,'0')}-${String(end.getDate()).padStart(2,'0')}`
    const list = await fetchRecords(startISO, endISO)
    setConsumptionRecords(list, 'replace')
  } catch (e) {
    console.error('loadRecentDays failed:', e)
  }
}
