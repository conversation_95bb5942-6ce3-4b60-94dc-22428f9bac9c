/**
 * 统计数据管理
 * 处理各种数据统计和分析功能
 */

import { computed } from 'vue'
import dayjs from '../utils/dayjs-setup.js'
import { parseToDayjs, getToday, getYesterday, getThisWeek, getThisMonth, daysBetween, formatDateOnly } from '../utils/dateUtils.js'

/**
 * 统计数据管理组合函数
 */
export const useStatistics = (consumptionRecords, activeBoxes, getBrand) => {
  
  /**
   * 今日统计数据
   */
  const todayStats = computed(() => {
    const today = getToday()
    const todayRecords = consumptionRecords.value.filter(record => {
      return !record.fake && formatDateOnly(record.timestamp) === today
    })
    
    let totalCigarettes = 0
    let totalPuffs = 0
    let totalSpent = 0
    
    todayRecords.forEach(record => {
      if (record.type === 'cigarette') {
        totalCigarettes += record.amount
      } else {
        totalPuffs += record.amount
      }
      // 新费用模型：优先使用记录的 spent
      const spent = typeof record.spent === 'number' ? record.spent : 0
      totalSpent += spent
    })
    
    return {
      totalCigarettes,
      totalPuffs,
      totalSpent: Math.round(totalSpent * 100) / 100,
      recordCount: todayRecords.length
    }
  })
  
  /**
   * 昨日统计数据
   */
  const yesterdayStats = computed(() => {
    const yesterday = getYesterday()
    const yesterdayRecords = consumptionRecords.value.filter(record => {
      return !record.fake && formatDateOnly(record.timestamp) === yesterday
    })
    
    let totalCigarettes = 0
    let totalPuffs = 0
    
    yesterdayRecords.forEach(record => {
      if (record.type === 'cigarette') {
        totalCigarettes += record.amount
      } else {
        totalPuffs += record.amount
      }
    })
    
    return { totalCigarettes, totalPuffs }
  })
  
  /**
   * 与昨日对比
   */
  const dailyComparison = computed(() => {
    const today = todayStats.value
    const yesterday = yesterdayStats.value
    
    return {
      cigaretteDiff: today.totalCigarettes - yesterday.totalCigarettes,
      puffDiff: today.totalPuffs - yesterday.totalPuffs
    }
  })
  
  /**
   * 本周统计数据
   */
  const weeklyStats = computed(() => {
    const { start, end } = getThisWeek()
    const weekRecords = consumptionRecords.value.filter(record => {
      const recordDate = formatDateOnly(record.timestamp)
      return !record.fake && recordDate >= start && recordDate <= end
    })
    
    let totalCigarettes = 0
    let totalPuffs = 0
    let totalSpent = 0
    
    weekRecords.forEach(record => {
      if (record.type === 'cigarette') {
        totalCigarettes += record.amount
      } else {
        totalPuffs += record.amount
      }
      totalSpent += (typeof record.spent === 'number' ? record.spent : 0)
    })
    
    return {
      totalCigarettes,
      totalPuffs,
      totalSpent: Math.round(totalSpent * 100) / 100,
      recordCount: weekRecords.length,
      dailyAverage: {
        cigarettes: Math.round(totalCigarettes / 7 * 10) / 10,
        puffs: Math.round(totalPuffs / 7 * 10) / 10
      }
    }
  })
  
  /**
   * 本月统计数据
   */
  const monthlyStats = computed(() => {
    const { start, end } = getThisMonth()
    const monthRecords = consumptionRecords.value.filter(record => {
      const recordDate = formatDateOnly(record.timestamp)
      return !record.fake && recordDate >= start && recordDate <= end
    })
    
    let totalCigarettes = 0
    let totalPuffs = 0
    let totalSpent = 0
    
    monthRecords.forEach(record => {
      if (record.type === 'cigarette') {
        totalCigarettes += record.amount
      } else {
        totalPuffs += record.amount
      }
      totalSpent += (typeof record.spent === 'number' ? record.spent : 0)
    })
    
    const daysInMonth = daysBetween(start, end) + 1
    
    return {
      totalCigarettes,
      totalPuffs,
      totalSpent: Math.round(totalSpent * 100) / 100,
      recordCount: monthRecords.length,
      dailyAverage: {
        cigarettes: Math.round(totalCigarettes / daysInMonth * 10) / 10,
        puffs: Math.round(totalPuffs / daysInMonth * 10) / 10
      }
    }
  })
  
  /**
   * 品牌使用统计
   */
  const brandStats = computed(() => {
    const brandUsage = {}
    
    consumptionRecords.value.forEach(record => {
      if (record.fake) return
      const box = activeBoxes.value.find(box => box.id === record.boxId)
      if (box) {
        const brand = getBrand(box.brandId)
        if (brand) {
          if (!brandUsage[brand.id]) {
            brandUsage[brand.id] = {
              brand,
              totalRecords: 0,
              totalCigarettes: 0,
              totalPuffs: 0,
              totalSpent: 0
            }
          }
          brandUsage[brand.id].totalRecords += 1
          if (record.type === 'cigarette') {
            brandUsage[brand.id].totalCigarettes += record.amount
          } else {
            brandUsage[brand.id].totalPuffs += record.amount
          }
          brandUsage[brand.id].totalSpent += (typeof record.spent === 'number' ? record.spent : 0)
        }
      }
    })
    
    return Object.values(brandUsage)
      .sort((a, b) => b.totalRecords - a.totalRecords)
      .map(item => ({
        ...item,
        totalSpent: Math.round(item.totalSpent * 100) / 100
      }))
  })
  
  /**
   * 获取指定日期的统计数据
   * @param {string} date 日期字符串 YYYY-MM-DD
   * @returns {object} 该日期的统计数据
   */
  const getDateStats = (date) => {
    const normalizedDate = typeof date === 'string' ? date.replace(/-/g, '/') : ''
    const dateRecords = consumptionRecords.value.filter(record => {
      return formatDateOnly(record.timestamp) === normalizedDate
    })
    
    let totalCigarettes = 0
    let totalPuffs = 0
    const recordsByTime = []
    
    dateRecords.forEach(record => {
      if (record.type === 'cigarette') {
        totalCigarettes += record.amount
      } else {
        totalPuffs += record.amount
      }
      
      const box = activeBoxes.value.find(box => box.id === record.boxId)
      const brand = box ? getBrand(box.brandId) : null
      
      recordsByTime.push({
        ...record,
        box,
        brand,
        time: parseToDayjs(record.timestamp).format('HH:mm')
      })
    })
    
    // 按时间排序
    recordsByTime.sort((a, b) => {
      const ta = typeof a.timestamp === 'number' ? a.timestamp : (parseToDayjs(a.timestamp).isValid() ? parseToDayjs(a.timestamp).valueOf() : 0)
      const tb = typeof b.timestamp === 'number' ? b.timestamp : (parseToDayjs(b.timestamp).isValid() ? parseToDayjs(b.timestamp).valueOf() : 0)
      return ta - tb
    })
    
    return {
      date,
      totalCigarettes,
      totalPuffs,
      recordCount: dateRecords.length,
      records: recordsByTime
    }
  }
  
  /**
   * 获取日历热力图数据
   * @param {string} startDate 开始日期
   * @param {string} endDate 结束日期
   * @returns {Array} 日历数据数组
   */
  const getCalendarHeatmap = (startDate, endDate) => {
    const result = []
    let cursor = dayjs(startDate, 'YYYY-MM-DD')
    const end = dayjs(endDate, 'YYYY-MM-DD')
    if (!cursor.isValid() || !end.isValid()) return result
    while (cursor.isSame(end, 'day') || cursor.isBefore(end, 'day')) {
      const dateStr = cursor.format('YYYY-MM-DD')
      const dayStats = getDateStats(dateStr)
      result.push({
        date: dateStr,
        value: dayStats.totalCigarettes + dayStats.totalPuffs,
        cigarettes: dayStats.totalCigarettes,
        puffs: dayStats.totalPuffs,
        records: dayStats.recordCount
      })
      cursor = cursor.add(1, 'day')
    }
    return result
  }

  /**
   * 获取日期范围统计
   * @param {string} startDate YYYY-MM-DD
   * @param {string} endDate YYYY-MM-DD
   */
  const getRangeStats = (startDate, endDate) => {
    if (!startDate || !endDate) {
      return { totalCigarettes: 0, totalPuffs: 0, totalSpent: 0, recordCount: 0, days: 0 }
    }

    // 规范顺序
    const start = dayjs(startDate, 'YYYY-MM-DD').isBefore(dayjs(endDate, 'YYYY-MM-DD')) ? startDate : endDate
    const end = dayjs(startDate, 'YYYY-MM-DD').isBefore(dayjs(endDate, 'YYYY-MM-DD')) ? endDate : startDate
    const startNormalized = start.replace(/-/g, '/')
    const endNormalized = end.replace(/-/g, '/')

    const recordsInRange = consumptionRecords.value.filter(record => {
      const d = formatDateOnly(record.timestamp)
      return !record.fake && d >= startNormalized && d <= endNormalized
    })

    let totalCigarettes = 0
    let totalPuffs = 0
    let totalSpent = 0
    const uniqueDays = new Set()

    recordsInRange.forEach(record => {
      const day = formatDateOnly(record.timestamp)
      uniqueDays.add(day)

      if (record.type === 'cigarette') {
        totalCigarettes += record.amount
      } else {
        totalPuffs += record.amount
      }
      totalSpent += (typeof record.spent === 'number' ? record.spent : 0)
    })

    return {
      startDate: startNormalized,
      endDate: endNormalized,
      totalCigarettes,
      totalPuffs,
      totalSpent: Math.round(totalSpent * 100) / 100,
      recordCount: recordsInRange.length,
      days: daysBetween(startNormalized, endNormalized) + 1
    }
  }

  /**
   * 全量累计统计（自开始使用至今）
   */
  const overallStats = computed(() => {
    let totalCigarettes = 0
    let totalPuffs = 0
    let totalSpent = 0
    const uniqueDays = new Set()

    consumptionRecords.value.forEach(record => {
      if (record.fake) return
      const day = formatDateOnly(record.timestamp)
      uniqueDays.add(day)

      if (record.type === 'cigarette') {
        totalCigarettes += record.amount
      } else {
        totalPuffs += record.amount
      }
      totalSpent += (typeof record.spent === 'number' ? record.spent : 0)
    })

    return {
      totalCigarettes,
      totalPuffs,
      totalSpent: Math.round(totalSpent * 100) / 100,
      totalDays: uniqueDays.size
    }
  })
  
  return {
    // 统计数据
    todayStats,
    yesterdayStats,
    dailyComparison,
    weeklyStats,
    monthlyStats,
    brandStats,
    overallStats,
    // 虚拟统计（小计）：可被页面读取
    todayVirtual: computed(() => {
      const today = getToday()
      let c = 0, p = 0
      consumptionRecords.value.forEach(r => { if (r.fake && formatDateOnly(r.timestamp) === today) { if (r.type === 'cigarette') c += r.amount; else p += r.amount } })
      return { totalCigarettes: c, totalPuffs: p, recordCount: c + p }
    }),
    weeklyVirtual: computed(() => {
      const { start, end } = getThisWeek()
      let c = 0, p = 0
      consumptionRecords.value.forEach(r => { const d = formatDateOnly(r.timestamp); if (r.fake && d >= start && d <= end) { if (r.type === 'cigarette') c += r.amount; else p += r.amount } })
      return { totalCigarettes: c, totalPuffs: p, recordCount: c + p }
    }),
    monthlyVirtual: computed(() => {
      const { start, end } = getThisMonth()
      let c = 0, p = 0
      consumptionRecords.value.forEach(r => { const d = formatDateOnly(r.timestamp); if (r.fake && d >= start && d <= end) { if (r.type === 'cigarette') c += r.amount; else p += r.amount } })
      return { totalCigarettes: c, totalPuffs: p, recordCount: c + p }
    }),
    
    // 方法
    getDateStats,
    getCalendarHeatmap,
    getRangeStats
  }
}
