/**
 * 用户设置状态管理
 * 管理用户偏好设置和个人信息，集成登录认证系统
 */

import { ref, computed } from 'vue'
import loginApi from '../utils/login.js'

// 响应式数据状态
const userSettings = ref({})
const userInfo = ref({})
const isLoggedIn = ref(false)

/**
 * 用户设置管理组合函数
 */
export const useUserSettings = () => {
  
  /**
   * 初始化用户数据
   */
  const initializeUserData = async () => {
    const loginStatus = loginApi.checkLoginStatus()
    isLoggedIn.value = loginStatus
    if (!loginStatus) {
      userInfo.value = {}
      userSettings.value = {}
      return
    }
      try {
        const userResult = await loginApi.getUserInfo()
      if (!userResult.success) throw new Error(userResult.message || '获取用户信息失败')
          const cloudUserInfo = userResult.data
          userInfo.value = {
            id: cloudUserInfo._id,
            nickname: cloudUserInfo.nickname,
            avatar: cloudUserInfo.avatar_url || cloudUserInfo.avatar_fileId,
            registeredDate: cloudUserInfo.register_time,
            totalRecordDays: cloudUserInfo.total_days
          }
      const p = cloudUserInfo.preferences || {}
      // 暴露云端累计统计给首页与其它页面使用
      userInfo.value = {
        ...userInfo.value,
        totalSpent: cloudUserInfo.total_spent || 0,
        totalCigarettes: cloudUserInfo.total_cigarettes || 0,
        totalPuffs: cloudUserInfo.total_puffs || 0,
        // totalRecordDays 已在上方设置，这里保持一致
        totalRecordDays: cloudUserInfo.total_days
      }

      userSettings.value = {
        theme: p.theme ?? 'light',
        reminderEnabled: p.reminder_enabled ?? true,
        maxActiveBoxes: p.max_active_boxes ?? 3,
        dailyLimit: p.daily_limit ?? { enabled: false, cigarettes: 10, puffs: 100 },
        defaultCigaretteCount: p.default_cigarette_count ?? 20,
        defaultElectronicPuffs: p.default_electronic_puffs ?? 300,
        hapticsEnabled: p.haptics_enabled ?? true,
        quickRecordPresets: p.quick_record_presets ?? { cigarette: [1,2,3], electronic: [5,10,20] },
        autoSuggestPresets: p.auto_suggest_presets ?? true
      }

      // 首登/老用户缺失字段时，补齐默认值到数据库
      const defaults = {
        theme: 'light',
        reminder_enabled: true,
        daily_limit: { enabled: false, cigarettes: 10, puffs: 100 },
        max_active_boxes: 3,
        default_cigarette_count: 20,
        default_electronic_puffs: 300,
        haptics_enabled: true,
        quick_record_presets: { cigarette: [1,2,3], electronic: [5,10,20] },
        auto_suggest_presets: true
      }
      const patch = {}
      if (p.theme === undefined) patch.theme = defaults.theme
      if (p.reminder_enabled === undefined) patch.reminder_enabled = defaults.reminder_enabled
      if (p.max_active_boxes === undefined) patch.max_active_boxes = defaults.max_active_boxes
      if (p.default_cigarette_count === undefined) patch.default_cigarette_count = defaults.default_cigarette_count
      if (p.default_electronic_puffs === undefined) patch.default_electronic_puffs = defaults.default_electronic_puffs
      if (p.haptics_enabled === undefined) patch.haptics_enabled = defaults.haptics_enabled
      if (p.auto_suggest_presets === undefined) patch.auto_suggest_presets = defaults.auto_suggest_presets

      // daily_limit 嵌套
      if (p.daily_limit === undefined) {
        patch.daily_limit = defaults.daily_limit
      } else {
        const needDl = {}
        if (p.daily_limit.enabled === undefined) needDl.enabled = defaults.daily_limit.enabled
        if (p.daily_limit.cigarettes === undefined) needDl.cigarettes = defaults.daily_limit.cigarettes
        if (p.daily_limit.puffs === undefined) needDl.puffs = defaults.daily_limit.puffs
        if (Object.keys(needDl).length) patch.daily_limit = needDl
      }

      // quick_record_presets 嵌套
      if (p.quick_record_presets === undefined) {
        patch.quick_record_presets = defaults.quick_record_presets
      } else {
        const needQr = {}
        if (p.quick_record_presets.cigarette === undefined) needQr.cigarette = defaults.quick_record_presets.cigarette
        if (p.quick_record_presets.electronic === undefined) needQr.electronic = defaults.quick_record_presets.electronic
        if (Object.keys(needQr).length) patch.quick_record_presets = needQr
      }

      if (Object.keys(patch).length) {
        const res = await loginApi.updatePreferences(patch)
        if (res.success) {
          applyServerPreferences(res.data)
        } else {
          console.warn('补齐默认偏好失败：', res.message)
        }
      }
    } catch (error) {
      console.error('获取云端用户信息失败:', error)
      uni.showToast({ title: '获取用户信息失败', icon: 'none' })
    }
  }
  
  // 本地缓存逻辑移除，服务器为唯一数据源

  // 资料是否完整（头像与昵称均存在）
  const isProfileComplete = computed(() => {
    const info = userInfo.value || {}
    return !!(info.nickname && info.nickname.trim()) && !!(info.avatar && String(info.avatar).trim())
  })

  // 通用守卫：需要用户信息的操作前检查
  const requireUserProfileForAction = async () => {
    if (!isLoggedIn.value || !isProfileComplete.value) {
      await new Promise((resolve) => {
        uni.showModal({
          title: '提示',
          content: '此操作需要完善头像和昵称，请前往设置完成资料后再试。',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({ url: '/pages/settings/settings' })
            }
            resolve()
          }
        })
      })
      return { allowed: false }
    }
    return { allowed: true }
  }
  
  /**
   * 更新用户设置
   * @param {object} newSettings 新的设置
   */
  const applyServerPreferences = (preferences) => {
    const p = preferences || {}
    userSettings.value = {
      theme: p.theme ?? 'light',
      reminderEnabled: p.reminder_enabled ?? true,
      maxActiveBoxes: p.max_active_boxes ?? 3,
      dailyLimit: p.daily_limit ?? { enabled: false, cigarettes: 10, puffs: 100 },
      defaultCigaretteCount: p.default_cigarette_count ?? 20,
      defaultElectronicPuffs: p.default_electronic_puffs ?? 300,
      hapticsEnabled: p.haptics_enabled ?? true,
      quickRecordPresets: p.quick_record_presets ?? { cigarette: [1,2,3], electronic: [5,10,20] },
      autoSuggestPresets: p.auto_suggest_presets ?? true
    }
  }
  
  /**
   * 更新用户信息
   * @param {object} newInfo 新的用户信息
   */
  const updateUserInfo = (newInfo) => {
    userInfo.value = { ...userInfo.value, ...newInfo }
  }
  
  /**
   * 设置最大活跃烟盒数量
   * @param {number} maxCount 最大数量
   */
  const setMaxActiveBoxes = async (maxCount) => {
    const prev = userSettings.value.maxActiveBoxes
    userSettings.value.maxActiveBoxes = maxCount
    const res = await loginApi.updatePreferences({ max_active_boxes: maxCount })
    if (res.success) { applyServerPreferences(res.data) } else { userSettings.value.maxActiveBoxes = prev; uni.showToast({ title: res.message || '保存失败', icon: 'none' }) }
  }
  
  /**
   * 设置提醒开关
   * @param {boolean} enabled 是否启用提醒
   */
  const setReminderEnabled = async (enabled) => {
    const prev = userSettings.value.reminderEnabled
    userSettings.value.reminderEnabled = enabled
    const res = await loginApi.updatePreferences({ reminder_enabled: !!enabled })
    if (res.success) { applyServerPreferences(res.data) } else { userSettings.value.reminderEnabled = prev; uni.showToast({ title: res.message || '保存失败', icon: 'none' }) }
  }
  
  /**
   * 设置每日限制
   * @param {object} dailyLimit 每日限制设置
   */
  const setDailyLimit = async (dailyLimit) => {
    const prev = { ...userSettings.value.dailyLimit }
    userSettings.value.dailyLimit = { ...prev, ...dailyLimit }
    const res = await loginApi.updatePreferences({ daily_limit: userSettings.value.dailyLimit })
    if (res.success) { applyServerPreferences(res.data) } else { userSettings.value.dailyLimit = prev; uni.showToast({ title: res.message || '保存失败', icon: 'none' }) }
  }
  
  /**
   * 设置默认规格
   * @param {object} specs 规格设置
   */
  const setDefaultSpecs = async (specs) => {
    const prevCount = userSettings.value.defaultCigaretteCount
    const prevPuffs = userSettings.value.defaultElectronicPuffs
    const next = {
      default_cigarette_count: specs.cigaretteCount ?? prevCount,
      default_electronic_puffs: specs.electronicPuffs ?? prevPuffs
    }
    userSettings.value.defaultCigaretteCount = next.default_cigarette_count
    userSettings.value.defaultElectronicPuffs = next.default_electronic_puffs
    const res = await loginApi.updatePreferences(next)
    if (res.success) { applyServerPreferences(res.data) } else {
      userSettings.value.defaultCigaretteCount = prevCount
      userSettings.value.defaultElectronicPuffs = prevPuffs
      uni.showToast({ title: res.message || '保存失败', icon: 'none' })
    }
  }

  /**
   * 设置快速记录预设
   * @param {{ cigarette?: number[], electronic?: number[] }} presets
   */
  const setQuickRecordPresets = async (presets) => {
    const prev = userSettings.value.quickRecordPresets || { cigarette: [1,2,3], electronic: [5,10,20] }
    const next = { ...prev, ...presets }
    userSettings.value.quickRecordPresets = next
    const res = await loginApi.updatePreferences({ quick_record_presets: next })
    if (res.success) { applyServerPreferences(res.data) } else { userSettings.value.quickRecordPresets = prev; uni.showToast({ title: res.message || '保存失败', icon: 'none' }) }
  }

  /**
   * 设置震动开关
   * @param {boolean} enabled
   */
  const setHapticsEnabled = async (enabled) => {
    const prev = userSettings.value.hapticsEnabled
    userSettings.value.hapticsEnabled = !!enabled
    const res = await loginApi.updatePreferences({ haptics_enabled: !!enabled })
    if (res.success) { applyServerPreferences(res.data) } else { userSettings.value.hapticsEnabled = prev; uni.showToast({ title: res.message || '保存失败', icon: 'none' }) }
  }

  /**
   * 设置主题
   * @param {'light'|'dark'} theme
   */
  const setTheme = async (theme) => {
    if (theme !== 'light' && theme !== 'dark') return
    const prev = userSettings.value.theme
    userSettings.value.theme = theme
    const res = await loginApi.updatePreferences({ theme })
    if (res.success) { applyServerPreferences(res.data) } else { userSettings.value.theme = prev; uni.showToast({ title: res.message || '保存失败', icon: 'none' }) }
  }
  
  /**
   * 重置设置为默认值
   */
  const resetSettings = async () => {
    const defaults = {
      theme: 'light',
      reminder_enabled: true,
      daily_limit: { enabled: false, cigarettes: 10, puffs: 100 },
      max_active_boxes: 3,
      default_cigarette_count: 20,
      default_electronic_puffs: 300,
      haptics_enabled: true,
      quick_record_presets: { cigarette: [1,2,3], electronic: [5,10,20] },
      auto_suggest_presets: true
    }
    const prevSnapshot = { ...userSettings.value }
    applyServerPreferences(defaults)
    const res = await loginApi.updatePreferences(defaults)
    if (res.success) { applyServerPreferences(res.data) } else { userSettings.value = prevSnapshot; uni.showToast({ title: res.message || '重置失败', icon: 'none' }) }
  }
  
  // 计算属性
  const isReminderEnabled = computed(() => userSettings.value.reminderEnabled)
  const maxActiveBoxes = computed(() => userSettings.value.maxActiveBoxes)
  const isDailyLimitEnabled = computed(() => userSettings.value.dailyLimit?.enabled || false)
  const dailyLimitSettings = computed(() => userSettings.value.dailyLimit || {})
  
  /**
   * 检查是否可以添加新烟盒
   * @param {number} currentActiveCount 当前活跃烟盒数量
   * @returns {boolean} 是否可以添加
   */
  const canAddNewBox = (currentActiveCount) => {
    return currentActiveCount < userSettings.value.maxActiveBoxes
  }
  
  /**
   * 检查是否超出每日限制
   * @param {number} todayCigarettes 今日香烟数量
   * @param {number} todayPuffs 今日电子烟口数
   * @returns {object} 检查结果
   */
  const checkDailyLimit = (todayCigarettes, todayPuffs) => {
    if (!userSettings.value.dailyLimit?.enabled) {
      return { exceeded: false, type: null }
    }
    
    const { cigarettes: maxCigarettes, puffs: maxPuffs } = userSettings.value.dailyLimit
    
    if (todayCigarettes >= maxCigarettes) {
      return { exceeded: true, type: 'cigarettes', current: todayCigarettes, limit: maxCigarettes }
    }
    
    if (todayPuffs >= maxPuffs) {
      return { exceeded: true, type: 'puffs', current: todayPuffs, limit: maxPuffs }
    }
    
    return { exceeded: false, type: null }
  }
  
  /**
   * 获取设置总结
   * @returns {object} 设置总结
   */
  const getSettingsSummary = () => {
    return {
      maxActiveBoxes: userSettings.value.maxActiveBoxes,
      reminderEnabled: userSettings.value.reminderEnabled,
      dailyLimitEnabled: userSettings.value.dailyLimit?.enabled || false,
      theme: userSettings.value.theme,
      hapticsEnabled: userSettings.value.hapticsEnabled,
      quickRecordPresets: userSettings.value.quickRecordPresets,
      autoSuggestPresets: userSettings.value.autoSuggestPresets,
      defaultSpecs: {
        cigarettes: userSettings.value.defaultCigaretteCount,
        electronicPuffs: userSettings.value.defaultElectronicPuffs
      }
    }
  }
  
  /**
   * 登录方法
   */
  const login = async () => {
    try {
      const result = await loginApi.login()
      if (result.success) {
        isLoggedIn.value = true
        // 重新初始化用户数据
        await initializeUserData()
        return result
      }
      return result
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, message: '登录失败' }
    }
  }

  /**
   * 退出登录方法
   */
  const logout = () => {
    const result = loginApi.logout()
    isLoggedIn.value = false
    userSettings.value = {}
    userInfo.value = {}
    return result
  }

  /**
   * 更新用户资料（同时更新云端和本地）
   */
  const updateUserProfile = async (profileData) => {
    try {
      if (isLoggedIn.value) {
        // 已登录，更新云端
        // 将通用 avatar 字段转换为后端所需的 avatar_fileId 或 avatar_url
        const payload = { nickname: profileData.nickname }
        if (profileData.avatar) {
          const avatarStr = String(profileData.avatar)
          const isUrl = /^(https?:)?\/\//i.test(avatarStr)
          if (isUrl) {
            payload.avatar_url = avatarStr
          } else {
            payload.avatar_fileId = avatarStr
          }
        }

        const result = await loginApi.updateUserInfo(payload)
        if (result.success) {
          // 更新本地用户信息
          userInfo.value = {
            ...userInfo.value,
            nickname: result.data.nickname,
            avatar: result.data.avatar_url || result.data.avatar_fileId
          }
          // 本地存储已移除，内存状态已更新即可
          return result
        }
        return result
      } else {
        return { success: false, message: '未登录' }
      }
    } catch (error) {
      console.error('更新用户资料失败:', error)
      return { success: false, message: '更新失败' }
    }
  }

  /**
   * 同步设置到云端
   */
  const syncSettingsToCloud = async () => ({ success: true })

  return {
    // 数据状态
    userSettings,
    userInfo,
    isLoggedIn,
    isProfileComplete,
    
    // 计算属性
    isReminderEnabled,
    maxActiveBoxes,
    isDailyLimitEnabled,
    dailyLimitSettings,
    
    // 方法
    initializeUserData,
    applyServerPreferences,
    updateUserInfo,
    setMaxActiveBoxes,
    setReminderEnabled,
    setDailyLimit,
    setDefaultSpecs,
    setTheme,
    resetSettings,
    canAddNewBox,
    checkDailyLimit,
    getSettingsSummary,
    setQuickRecordPresets,
    setHapticsEnabled,
    requireUserProfileForAction,
    
    // 登录相关方法
    login,
    logout,
    updateUserProfile,
    syncSettingsToCloud,
    /**
     * 刷新用户聚合统计（总花费/总卷烟/总口数/记录天数）
     * 与后端口径保持一致
     */
    async refreshUserAggregate() {
      try {
        const result = await loginApi.getUserInfo()
        if (!result || !result.success || !result.data) return { success: false }
        const cloudUserInfo = result.data
        userInfo.value = {
          ...userInfo.value,
          totalSpent: cloudUserInfo.total_spent || 0,
          totalCigarettes: cloudUserInfo.total_cigarettes || 0,
          totalPuffs: cloudUserInfo.total_puffs || 0,
          totalRecordDays: cloudUserInfo.total_days || 0
        }
        return { success: true }
      } catch (e) {
        console.warn('refreshUserAggregate failed:', e)
        return { success: false }
      }
    }
  }
}
