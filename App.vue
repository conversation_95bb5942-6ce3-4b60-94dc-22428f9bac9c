<script setup>
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import loginApi from './utils/login.js'
import { watch } from 'vue'
import { useTheme } from './composables/useTheme.js'

onLaunch(async () => {
	console.log('App Launch')
	
	// 应用启动时的自动登录检查（静默尝试，不做路由跳转）
	try {
		const loginCheckResult = await loginApi.autoLoginCheck()
		console.log('Auto login check finished:', loginCheckResult)
	} catch (error) {
		console.error('自动登录检查失败:', error)
		// 失败不打断进入应用
	}
})

onShow(() => {
	console.log('App Show')
	// 应用从后台进入前台
})

onHide(() => {
	console.log('App Hide')
	// 应用从前台进入后台
})

// H5 环境：将主题类同步到 document.documentElement，确保全局覆盖层与第三方样式一致
const { currentTheme } = useTheme()
watch(currentTheme, (cls) => {
  try {
    if (typeof document !== 'undefined' && document && document.documentElement && cls) {
      const root = document.documentElement
      root.classList.remove('theme-light', 'theme-dark')
      root.classList.add(cls)
    }
  } catch (_) {}
}, { immediate: true })
</script>

<style lang="scss">
/*每个页面公共css */
@import './uni.scss';

/* 全局样式重置（支持运行时主题） */
page {
    background-color: var(--color-bg-page);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 通用样式类 */
.flex {
	display: flex;
}

.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}

.flex-between {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.text-primary { color: var(--color-text-primary); }

.text-secondary { color: var(--color-text-secondary); }

.text-muted { color: var(--color-text-muted); }

.card {
    background-color: var(--color-bg-card);
    border-radius: $app-card-radius;
    box-shadow: var(--shadow-sm);
}

.btn-primary {
    background-color: var(--color-primary);
    color: var(--color-text-white);
    border-radius: $app-button-radius;
    border: none;
    padding: $app-spacing-sm $app-spacing-md;
    font-size: $app-font-size-base;
    &:active { background-color: var(--color-primary-press); }
}

.btn-secondary {
    background-color: var(--color-secondary);
    color: var(--color-text-white);
    border-radius: $app-button-radius;
    border: none;
    padding: $app-spacing-sm $app-spacing-md;
    font-size: $app-font-size-base;
    &:active { opacity: 0.8; }
}
</style>
