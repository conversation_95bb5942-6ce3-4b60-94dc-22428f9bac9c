/**
 * Achievements cloud API wrapper
 */

import loginApi from './login.js'

let cloud = null
function init() { if (!cloud) cloud = uniCloud.importObject('achievements'); return cloud }

async function getValidToken() {
  let token = loginApi.storage.getToken()
  if (!token) throw new Error('用户未登录')
  if (loginApi.isTokenExpiringSoon()) {
    const r = await loginApi.refreshToken()
    if (r.success && r.token) token = r.token; else throw new Error('Token已过期，请重新登录')
  }
  return token
}

async function call(method, ...args) {
  try {
    const token = await getValidToken()
    const api = init()
    const res = await api[method](token, ...args)
    if (!res || res.code !== 200) {
      if (res && res.code === 401) { loginApi.storage.clearAll(); throw new Error('登录已过期，请重新登录') }
      throw new Error((res && res.message) || '服务异常')
    }
    return { success: true, data: res.data }
  } catch (e) {
    console.error(`achievements.${method} error:`, e)
    return { success: false, message: e.message || '服务异常' }
  }
}

export default {
  getUserAchievements() { return call('getUserAchievements') },
  syncUnlocked(payload) { return call('syncUnlocked', payload) },
  markRead(ids) { return call('markRead', { ids }) },
  getUnreadCount() { return call('getUnreadCount') }
}


