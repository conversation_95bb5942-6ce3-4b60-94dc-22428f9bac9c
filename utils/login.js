/**
 * 登录认证工具类
 */

import { addMilliseconds, isExpiringSoon, isExpired, now } from "./dateUtils.js";

// API配置
const CONFIG = {
  TOKEN_KEY: "cigarette_book_token",
  TOKEN_EXPIRES_AT_KEY: "cigarette_book_token_expires_at", 
  USER_INFO_KEY: "cigarette_book_user_info",
};

// 云对象实例
let userCloudObject = null;

/**
 * 初始化云对象
 */
function initCloudObject() {
  if (!userCloudObject) {
    userCloudObject = uniCloud.importObject("user");
  }
  return userCloudObject;
}

/**
 * 本地存储工具
 */
const storage = {
  // 获取token
  getToken() {
    return uni.getStorageSync(CONFIG.TOKEN_KEY) || null;
  },

  // 设置token
  setToken(token) {
    uni.setStorageSync(CONFIG.TOKEN_KEY, token);
  },

  // 清除token
  clearToken() {
    uni.removeStorageSync(CONFIG.TOKEN_KEY);
  },

  getTokenExpiresAt() {
    const expiresAt = uni.getStorageSync(CONFIG.TOKEN_EXPIRES_AT_KEY);
    return expiresAt ? parseInt(expiresAt, 10) : null;
  },

  setTokenExpiresAt(expiresInSeconds) {
    const expiresAt = addMilliseconds(now(), expiresInSeconds * 1000);
    uni.setStorageSync(CONFIG.TOKEN_EXPIRES_AT_KEY, expiresAt.toString());
  },

  clearTokenExpiresAt() {
    uni.removeStorageSync(CONFIG.TOKEN_EXPIRES_AT_KEY);
  },

  // 获取用户信息
  getUserInfo() {
    const userInfo = uni.getStorageSync(CONFIG.USER_INFO_KEY);
    return userInfo ? JSON.parse(userInfo) : null;
  },

  // 设置用户信息
  setUserInfo(userInfo) {
    uni.setStorageSync(CONFIG.USER_INFO_KEY, JSON.stringify(userInfo));
  },

  // 清除用户信息
  clearUserInfo() {
    uni.removeStorageSync(CONFIG.USER_INFO_KEY);
  },

  // 清除所有认证数据
  clearAll() {
    this.clearToken();
    this.clearTokenExpiresAt();
    this.clearUserInfo();
  },
};

/**
 * 检查token是否即将过期
 */
function isTokenExpiringSoon() {
  const expiresAt = storage.getTokenExpiresAt();
  return isExpiringSoon(expiresAt, 30); // 30分钟阈值
}

/**
 * 微信登录
 */
async function login() {
  try {
    // 1. 获取微信登录code
    const loginResult = await new Promise((resolve, reject) => {
      uni.login({
        provider: "weixin",
        success: resolve,
        fail: reject,
      });
    });

    if (!loginResult.code) {
      throw new Error("获取微信登录code失败");
    }

    // 2. 调用云对象登录
    const cloudObject = initCloudObject();
    const result = await cloudObject.login(loginResult.code);

    if (result.code !== 200) {
      throw new Error(result.message || "登录失败");
    }

    // 3. 保存token和用户信息
    storage.setToken(result.data.token);
    storage.setTokenExpiresAt(result.data.expiresIn);
    storage.setUserInfo(result.data.userInfo);

    return {
      success: true,
      data: {
        token: result.data.token,
        userInfo: result.data.userInfo,
        isNewUser: result.data.isNewUser,
        expiresIn: result.data.expiresIn,
      },
    };
  } catch (error) {
    console.error("登录失败:", error);
    return {
      success: false,
      message: error.message || "登录失败",
    };
  }
}

/**
 * 刷新token
 */
async function refreshToken() {
  try {
    const currentToken = storage.getToken();
    if (!currentToken) {
      throw new Error("没有可刷新的token");
    }

    const cloudObject = initCloudObject();
    const result = await cloudObject.refreshToken(currentToken);

    if (result.code !== 200) {
      throw new Error(result.message || "Token刷新失败");
    }

    // 保存新token
    storage.setToken(result.data.token);
    storage.setTokenExpiresAt(result.data.expiresIn);

    return {
      success: true,
      token: result.data.token,
    };
  } catch (error) {
    console.error("Token刷新失败:", error);
    // 刷新失败时清除本地数据
    storage.clearAll();
    return {
      success: false,
      message: error.message || "Token刷新失败",
    };
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo() {
  try {
    let token = storage.getToken();

    if (!token) {
      throw new Error("用户未登录");
    }
    
    // 检查token是否即将过期，如果是则先刷新
    if (isTokenExpiringSoon()) {
      console.log("Token即将过期，尝试刷新...");
      const refreshResult = await refreshToken();
      if (refreshResult.success) {
        token = refreshResult.token;
      } else {
        throw new Error("Token已过期，请重新登录");
      }
    }

    const cloudObject = initCloudObject();
    const result = await cloudObject.getUserInfo(token);

    if (result.code !== 200) {
      if (result.code === 401) {
        // token无效，清除本地数据
        storage.clearAll();
        throw new Error("登录已过期，请重新登录");
      }
      throw new Error(result.message || "获取用户信息失败");
    }

    // 更新本地用户信息
    storage.setUserInfo(result.data);

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return {
      success: false,
      message: error.message || "获取用户信息失败",
    };
  }
}

/**
 * 更新用户信息
 */
async function updateUserInfo(userInfo) {
  try {
    let token = storage.getToken();

    if (!token) {
      throw new Error("用户未登录");
    }

    // 检查token是否即将过期
    if (isTokenExpiringSoon()) {
      const refreshResult = await refreshToken();
      if (refreshResult.success) {
        token = refreshResult.token;
      } else {
        throw new Error("Token已过期，请重新登录");
      }
    }

    const cloudObject = initCloudObject();
    const result = await cloudObject.updateUserInfo(token, userInfo);

    if (result.code !== 200) {
      if (result.code === 401) {
        storage.clearAll();
        throw new Error("登录已过期，请重新登录");
      }
      throw new Error(result.message || "更新用户信息失败");
    }

    // 使用云对象返回的更新后的用户信息更新本地存储
    if (result.data) {
      storage.setUserInfo(result.data);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("更新用户信息失败:", error);
    return {
      success: false,
      message: error.message || "更新用户信息失败",
    };
  }
}

/**
 * 局部更新用户偏好设置（云对象合并）
 */
async function updatePreferences(patch) {
  try {
    let token = storage.getToken();
    if (!token) {
      throw new Error("用户未登录");
    }

    if (isTokenExpiringSoon()) {
      const refreshResult = await refreshToken();
      if (refreshResult.success) {
        token = refreshResult.token;
      } else {
        throw new Error("Token已过期，请重新登录");
      }
    }

    const cloudObject = initCloudObject();
    const result = await cloudObject.updatePreferences(token, patch);

    if (result.code !== 200) {
      if (result.code === 401) {
        storage.clearAll();
        throw new Error("登录已过期，请重新登录");
      }
      throw new Error(result.message || "更新偏好设置失败");
    }

    // 同步最新用户信息到本地缓存
    const userInfo = storage.getUserInfo() || {};
    const merged = { ...userInfo, preferences: result.data };
    storage.setUserInfo(merged);

    return { success: true, data: result.data };
  } catch (error) {
    console.error("更新偏好设置失败:", error);
    return { success: false, message: error.message || "更新偏好设置失败" };
  }
}

/**
 * 检查登录状态
 */
function checkLoginStatus() {
  const token = storage.getToken();
  const userInfo = storage.getUserInfo();

  if (!token || !userInfo) {
    return false;
  }

  // 检查token是否过期
  const expiresAt = storage.getTokenExpiresAt();
  return !isExpired(expiresAt);
}

/**
 * 退出登录
 */
function logout() {
  storage.clearAll();

  console.log("用户已退出登录");

  return {
    success: true,
    message: "退出登录成功",
  };
}

/**
 * 通过临时code检查用户是否已注册（不创建账号）
 */
async function checkUserExists() {
  try {
    const loginResult = await new Promise((resolve, reject) => {
      uni.login({ provider: "weixin", success: resolve, fail: reject });
    });
    if (!loginResult.code) throw new Error("获取微信登录code失败");
    const cloudObject = initCloudObject();
    const result = await cloudObject.checkUserExists(loginResult.code);
    if (result.code !== 200) throw new Error(result.message || "检查失败");
    return { success: true, exists: !!(result.data && result.data.exists) };
  } catch (e) {
    console.warn("检查用户是否存在失败", e);
    return { success: false };
  }
}

/**
 * 如果需要则静默登录（不打断导航，不跳转页面）
 */
async function silentLoginIfNeeded() {
  try {
    // 已有有效登录态，尝试获取最新用户信息以确保本地资料有效
    if (checkLoginStatus()) {
      const info = await getUserInfo();
      return info.success ? { success: true, alreadyLoggedIn: true } : { success: false };
    }
    // 无登录态，尝试静默登录
    const result = await login();
    return result.success ? { success: true } : { success: false };
  } catch (e) {
    console.warn("静默登录失败（忽略并继续进入应用）", e);
    return { success: false };
  }
}

/**
 * 自动登录检查（应用启动时调用）
 * 改造后：内部尝试静默登录，不再返回 needLogin=true 的语义
 */
async function autoLoginCheck() {
  try {
    if (checkLoginStatus()) {
      const userResult = await getUserInfo();
      if (userResult.success) {
        return { success: true, needLogin: false, data: userResult.data };
      }
      // 获取用户信息失败，清空后继续静默登录一次
      storage.clearAll();
    }

    // 尝试静默登录
    const silent = await silentLoginIfNeeded();
    return silent.success
      ? { success: true, needLogin: false }
      : { success: false, needLogin: false };
  } catch (error) {
    console.error("自动登录检查失败:", error);
    storage.clearAll();
    return { success: false, needLogin: false };
  }
}

// 导出API
export default {
  // 登录相关
  login,
  logout,
  checkLoginStatus,
  autoLoginCheck,
  silentLoginIfNeeded,
  checkUserExists,

  // 用户信息相关
  getUserInfo,
  updateUserInfo,
  updatePreferences,

  // Token相关
  refreshToken,
  isTokenExpiringSoon,

  // 存储相关
  storage,
};
