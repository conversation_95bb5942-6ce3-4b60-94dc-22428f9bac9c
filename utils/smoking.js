/**
 * Smoking cloud API wrapper
 */

import loginApi from './login.js'

let smokingCloud = null

function initCloudObject() {
  if (!smokingCloud) smokingCloud = uniCloud.importObject('smoking')
  return smokingCloud
}

async function getValidToken() {
  let token = loginApi.storage.getToken()
  if (!token) throw new Error('用户未登录')
  if (loginApi.isTokenExpiringSoon()) {
    const r = await loginApi.refreshToken()
    if (r.success && r.token) token = r.token
    else throw new Error('Token已过期，请重新登录')
  }
  return token
}

async function call(method, ...args) {
  try {
    const token = await getValidToken()
    const api = initCloudObject()
    const res = await api[method](token, ...args)
    if (!res || res.code !== 200) {
      if (res && res.code === 401) {
        loginApi.storage.clearAll()
        throw new Error('登录已过期，请重新登录')
      }
      throw new Error((res && res.message) || '服务异常')
    }
    return { success: true, data: res.data }
  } catch (e) {
    console.error(`smoking.${method} error:`, e)
    return { success: false, message: e.message || '服务异常' }
  }
}

// Brands
async function getBrands(options = {}) { return call('getBrands', options) }
async function addCustomBrand(payload) { return call('addCustomBrand', payload) }
async function updateBrand(brandId, patch) { return call('updateBrand', brandId, patch) }
async function removeBrand(brandId) { return call('removeBrand', brandId) }

// Boxes
async function listBoxes(options = {}) { return call('listBoxes', options) }
async function createBox(payload) { return call('createBox', payload) }
async function updateBox(boxId, patch) { return call('updateBox', boxId, patch) }
async function completeBox(boxId) { return call('completeBox', boxId) }
async function completeCartridge(boxId) { return call('completeCartridge', { boxId }) }
async function removeBox(boxId) { return call('removeBox', boxId) }

// Records
async function addRecord(payload) { return call('addRecord', payload) }
async function undoRecord(recordId) { return call('undoRecord', { recordId }) }
async function updateRecord(recordId, payload) { return call('updateRecord', { recordId, ...payload }) }
async function listRecords(params = {}) { return call('listRecords', params) }
async function addVirtualRecord(payload) { return call('addVirtualRecord', payload) }

export default {
  getBrands,
  addCustomBrand,
  updateBrand,
  removeBrand,
  listBoxes,
  createBox,
  updateBox,
  completeBox,
  completeCartridge,
  removeBox,
  addRecord,
  addVirtualRecord,
  undoRecord,
  updateRecord,
  listRecords
}


