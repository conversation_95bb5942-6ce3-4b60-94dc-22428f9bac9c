/**
 * 日期处理工具类（基于 dayjs + Asia/Shanghai 时区）
 * 提供日期格式化、比较、计算等功能
 */
import dayjs from './dayjs-setup.js'

// Robust parser to handle multiple date string formats across platforms (incl. iOS)
const KNOWN_FORMATS = [
  'YYYY-MM-DD HH:mm:ss',
  'YYYY/MM/DD HH:mm:ss',
  'YYYY-MM-DD',
  'YYYY/MM/DD',
  'YYYY-MM-DDTHH:mm:ss',
  'YYYY-MM-DDTHH:mm:ssZ',
  'YYYY-MM-DDTHH:mm:ss+HH:mm',
  // Locale-like US formats sometimes seen from toLocaleString
  'M/D/YYYY, h:mm:ss A',
  'M/D/YYYY, h:mm A',
  'M/D/YYYY h:mm:ss A',
  'M/D/YYYY h:mm A'
]

export const parseToDayjs = (input) => {
  if (input === null || input === undefined) return dayjs(NaN)
  if (typeof input === 'number') {
    const tsMs = input < 1e12 ? input * 1000 : input
    return dayjs(tsMs)
  }
  if (input instanceof Date) {
    return dayjs(input)
  }
  if (typeof input !== 'string') return dayjs(NaN)
  const trimmed = input.trim()
  // Try native first (ISO, RFC etc.)
  let d = dayjs(trimmed)
  if (d.isValid()) return d
  // Try known formats explicitly
  for (const fmt of KNOWN_FORMATS) {
    d = dayjs(trimmed, fmt)
    if (d.isValid()) return d
  }
  return dayjs(NaN)
}

/**
 * 格式化日期
 * @param {Date|string} date 日期对象或字符串
 * @param {string} format 格式化模板 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY/MM/DD HH:mm:ss') => {
  const d = parseToDayjs(date)
  if (!d.isValid()) return ''
  return d.format(format)
}

/**
 * 统一标准格式化（自动选择仅日期或日期时间）
 * - 若时间部分为 00:00:00 则返回 YYYY/MM/DD
 * - 否则返回 YYYY/MM/DD HH:mm:ss
 */
export const formatStandard = (date) => {
  const d = parseToDayjs(date)
  if (!d.isValid()) return ''
  if (d.hour() === 0 && d.minute() === 0 && d.second() === 0) {
    return d.format('YYYY/MM/DD')
  }
  return d.format('YYYY/MM/DD HH:mm:ss')
}

export const formatDateOnly = (date) => {
  const d = parseToDayjs(date)
  if (!d.isValid()) return ''
  return d.format('YYYY/MM/DD')
}

export const formatDateTime = (date) => {
  const d = parseToDayjs(date)
  if (!d.isValid()) return ''
  return d.format('YYYY/MM/DD HH:mm:ss')
}

/**
 * 获取今天的日期字符串
 * @param {string} format 格式化模板
 * @returns {string} 今天的日期
 */
export const getToday = (format = 'YYYY/MM/DD') => {
  return dayjs().format(format)
}

/**
 * 获取昨天的日期字符串
 * @param {string} format 格式化模板
 * @returns {string} 昨天的日期
 */
export const getYesterday = (format = 'YYYY/MM/DD') => {
  return dayjs().subtract(1, 'day').format(format)
}

/**
 * 判断是否为今天
 * @param {Date|string} date 要判断的日期
 * @returns {boolean} 是否为今天
 */
export const isToday = (date) => {
  const d = parseToDayjs(date)
  if (!d.isValid()) return false
  return d.isSame(dayjs(), 'day')
}

/**
 * 判断是否为昨天
 * @param {Date|string} date 要判断的日期
 * @returns {boolean} 是否为昨天
 */
export const isYesterday = (date) => {
  const d = parseToDayjs(date)
  if (!d.isValid()) return false
  return d.isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string} date1 开始日期
 * @param {Date|string} date2 结束日期
 * @returns {number} 天数差
 */
export const daysBetween = (date1, date2) => {
  const d1 = parseToDayjs(date1)
  const d2 = parseToDayjs(date2)
  if (!d1.isValid() || !d2.isValid()) return 0
  return Math.abs(d2.diff(d1, 'day'))
}

/**
 * 获取本周的开始和结束日期
 * @returns {object} {start: string, end: string}
 */
export const getThisWeek = () => {
  const start = dayjs().startOf('week')
  const end = dayjs().endOf('week')
  return {
    start: start.format('YYYY/MM/DD'),
    end: end.format('YYYY/MM/DD')
  }
}

/**
 * 获取本月的开始和结束日期
 * @returns {object} {start: string, end: string}
 */
export const getThisMonth = () => {
  const start = dayjs().startOf('month')
  const end = dayjs().endOf('month')
  return {
    start: start.format('YYYY/MM/DD'),
    end: end.format('YYYY/MM/DD')
  }
}

/**
 * 获取本年的开始和结束日期
 * @returns {object} {start: string, end: string, year: number}
 */
export const getThisYear = () => {
  const start = dayjs().startOf('year')
  const end = dayjs().endOf('year')
  return {
    start: start.format('YYYY/MM/DD'),
    end: end.format('YYYY/MM/DD'),
    year: dayjs().year()
  }
}

/**
 * 根据偏移量获取周范围（offset=0本周，-1上周，1下周）
 */
export const getWeekRangeByOffset = (offset = 0) => {
  const base = dayjs().add(offset, 'week')
  const start = base.startOf('week')
  const end = base.endOf('week')
  const label = `${start.format('MM/DD')} ~ ${end.format('MM/DD')}`
  return { start: start.format('YYYY/MM/DD'), end: end.format('YYYY/MM/DD'), label }
}

/**
 * 根据偏移量获取月范围（offset=0本月）
 */
export const getMonthRangeByOffset = (offset = 0) => {
  const base = dayjs().add(offset, 'month')
  const start = base.startOf('month')
  const end = base.endOf('month')
  const label = `${base.year()}年${base.month() + 1}月`
  return { start: start.format('YYYY/MM/DD'), end: end.format('YYYY/MM/DD'), label }
}

/**
 * 根据偏移量获取年范围（offset=0本年）
 */
export const getYearRangeByOffset = (offset = 0) => {
  const base = dayjs().add(offset, 'year')
  const start = base.startOf('year')
  const end = base.endOf('year')
  const label = `${base.year()}年`
  return { start: start.format('YYYY/MM/DD'), end: end.format('YYYY/MM/DD'), label, year: base.year() }
}

/**
 * 列举从 start 到 end 的所有日期（包含端点），格式 YYYY/MM/DD
 */
export const enumerateDays = (startStr, endStr) => {
  const arr = []
  let cursor = dayjs(startStr, 'YYYY/MM/DD')
  const end = dayjs(endStr, 'YYYY/MM/DD')
  if (!cursor.isValid() || !end.isValid()) return arr
  while (cursor.isSame(end, 'day') || cursor.isBefore(end, 'day')) {
    arr.push(cursor.format('YYYY/MM/DD'))
    cursor = cursor.add(1, 'day')
  }
  return arr
}

/**
 * 列举指定年的12个月（1..12）标签
 */
export const enumerateMonthsOfYear = (year) => {
  const arr = []
  for (let m = 0; m < 12; m++) {
    arr.push(dayjs(`${year}/${String(m + 1).padStart(2, '0')}/01`, 'YYYY/MM/DD').format('YYYY/MM'))
  }
  return arr
}

/**
 * 获取相对时间描述
 * @param {Date|string} date 目标日期
 * @returns {string} 相对时间描述
 */
export const getRelativeTime = (date) => {
  const now = dayjs()
  const target = parseToDayjs(date)
  if (!target.isValid()) return ''
  const diffMs = now.valueOf() - target.valueOf()
  const minutes = Math.floor(diffMs / (1000 * 60))
  const hours = Math.floor(diffMs / (1000 * 60 * 60))
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return formatDate(date, 'YYYY/MM/DD')
}

/**
 * 生成唯一的时间戳ID
 * @returns {string} 时间戳ID
 */
export const generateTimeId = () => {
  return dayjs().valueOf().toString() + Math.random().toString(36).substr(2, 9)
}

/**
 * 获取当前时间戳（毫秒）
 * @returns {number} 当前时间戳
 */
export const now = () => {
  return dayjs().valueOf()
}

/**
 * 在指定时间上添加毫秒数
 * @param {number} timestamp 基础时间戳
 * @param {number} milliseconds 要添加的毫秒数
 * @returns {number} 计算后的时间戳
 */
export const addMilliseconds = (timestamp, milliseconds) => {
  return timestamp + milliseconds
}

/**
 * 检查时间是否即将过期
 * @param {number} expiresAt 过期时间戳
 * @param {number} thresholdMinutes 阈值分钟数，默认30分钟
 * @returns {boolean} 是否即将过期
 */
export const isExpiringSoon = (expiresAt, thresholdMinutes = 30) => {
  if (!expiresAt) return true
  const currentTime = now()
  const thresholdMs = thresholdMinutes * 60 * 1000
  return (expiresAt - currentTime) <= thresholdMs
}

/**
 * 检查时间是否已过期
 * @param {number} expiresAt 过期时间戳
 * @returns {boolean} 是否已过期
 */
export const isExpired = (expiresAt) => {
  if (!expiresAt) return true
  return now() >= expiresAt
}
