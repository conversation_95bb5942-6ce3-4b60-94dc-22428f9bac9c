// 统一的数字与金额格式化工具
// 依赖 numeral（已安装）

import numeral from 'numeral'

/**
 * 将值格式化为人民币金额，带千分位与两位小数。
 * 返回示例："¥1,234.56"
 */
export const formatCurrency = (value) => {
  const num = Number(value)
  if (Number.isNaN(num)) return '¥—'
  return `¥${numeral(num).format('0,0.00')}`
}

/**
 * 将值格式化为整数显示（带千分位）。
 * 返回示例："1,234"
 */
export const formatInteger = (value) => {
  const num = Number(value)
  if (Number.isNaN(num)) return '—'
  return numeral(Math.round(num)).format('0,0')
}

/**
 * 紧凑格式（如 1.2k、3.4m），用于概览性数值。
 */
export const formatCompact = (value) => {
  const num = Number(value)
  if (Number.isNaN(num)) return '—'
  // numeral 没有原生 compact，做一个简易版本
  if (Math.abs(num) >= 1_000_000) return `${numeral(num / 1_000_000).format('0.0')}m`
  if (Math.abs(num) >= 1_000) return `${numeral(num / 1_000).format('0.0')}k`
  return numeral(num).format('0,0')
}

export default {
  formatCurrency,
  formatInteger,
  formatCompact
}


