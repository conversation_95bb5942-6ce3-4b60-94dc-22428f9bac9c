/**
 * 本地存储工具类
 * 统一管理应用的本地数据存储
 */

// 存储键名常量
export const STORAGE_KEYS = {
  USER_SETTINGS: 'cigarette_book_user_settings',
  ACTIVE_BOXES: 'cigarette_book_active_boxes', 
  CONSUMPTION_RECORDS: 'cigarette_book_consumption_records',
  BRAND_COLLECTION: 'cigarette_book_brand_collection',
  CUSTOM_BRANDS: 'cigarette_book_custom_brands',
  USER_INFO: 'cigarette_book_user_info',
  UI_STATE: 'cigarette_book_ui_state'
}

/**
 * 设置本地存储数据
 * @param {string} key 存储键
 * @param {any} data 要存储的数据
 */
export const setStorage = (key, data) => {
  try {
    uni.setStorageSync(key, JSON.stringify(data))
    return true
  } catch (error) {
    console.error('存储数据失败:', error)
    return false
  }
}

/**
 * 获取本地存储数据
 * @param {string} key 存储键
 * @param {any} defaultValue 默认值
 * @returns {any} 存储的数据或默认值
 */
export const getStorage = (key, defaultValue = null) => {
  try {
    const data = uni.getStorageSync(key)
    return data ? JSON.parse(data) : defaultValue
  } catch (error) {
    console.error('读取数据失败:', error)
    return defaultValue
  }
}

/**
 * 删除本地存储数据
 * @param {string} key 存储键
 */
export const removeStorage = (key) => {
  try {
    uni.removeStorageSync(key)
    return true
  } catch (error) {
    console.error('删除数据失败:', error)
    return false
  }
}

/**
 * 清空所有应用数据
 */
export const clearAppStorage = () => {
  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      uni.removeStorageSync(key)
    })
    return true
  } catch (error) {
    console.error('清空数据失败:', error)
    return false
  }
}

/**
 * 获取存储信息
 */
export const getStorageInfo = () => {
  try {
    return uni.getStorageInfoSync()
  } catch (error) {
    console.error('获取存储信息失败:', error)
    return null
  }
}
